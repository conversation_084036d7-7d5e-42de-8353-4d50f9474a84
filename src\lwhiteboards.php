<?php

#region region DOCS
/** @var Whiteboard[] $whiteboards */
/** @var Whiteboard $newwhiteboard */
#endregion docs

global $conexion;

use App\classes\Whiteboard;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/general/preparar.php';

$newwhiteboard = new Whiteboard();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_GET['m'])) {
            $success_display = 'show';
            $success_text = 'El whiteboard ha sido modificado.';
        }
        if (isset($_GET['u'])) {
            $success_display = 'show';
            $success_text = 'El whiteboard ha sido actualizado.';
        }
        if (isset($_GET['c'])) {
            $success_display = 'show';
            $success_text = 'El whiteboard ha sido creado.';
        }
        if (isset($_GET['d'])) {
            $success_display = 'show';
            $success_text = 'El whiteboard ha sido eliminado.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get

#region sub_save_whiteboard
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_save_whiteboard'])) {
    try {
        // Basic validation
        $name       = limpiar_datos($_POST['whiteboard_name'] ?? '');
        $image_data = $_POST['whiteboard_data'] ?? '';
        $width      = (int)($_POST['whiteboard_width'] ?? 800);
        $height     = (int)($_POST['whiteboard_height'] ?? 600);
        $overwrite  = isset($_POST['overwrite_existing']) && $_POST['overwrite_existing'] === '1';

        if (empty($name)) {
            throw new Exception('El nombre del whiteboard es requerido.');
        }

        if (empty($image_data)) {
            throw new Exception('No hay datos de imagen para guardar.');
        }

        // Check if whiteboard with same name exists
        $existingWhiteboard = Whiteboard::obtenerPorNombre($name, $conexion);

        if ($existingWhiteboard && !$overwrite) {
            // Return JSON response indicating name conflict
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'name_exists' => true,
                'existing_id' => $existingWhiteboard->getId(),
                'message' => 'Ya existe un whiteboard con este nombre. ¿Desea sobrescribirlo?'
            ]);
            exit();
        }

        if ($existingWhiteboard && $overwrite) {
            // Check if specific ID was provided for overwrite (priority order: overwrite_id, current_whiteboard_id)
            $overwriteId = isset($_POST['overwrite_id']) ? (int)$_POST['overwrite_id'] : null;
            $currentWhiteboardId = isset($_POST['current_whiteboard_id']) ? (int)$_POST['current_whiteboard_id'] : null;

            // Use overwrite_id if provided, otherwise use current_whiteboard_id
            $targetId = $overwriteId ?: $currentWhiteboardId;

            if ($targetId) {
                // Overwrite specific whiteboard by ID
                $whiteboardToUpdate = Whiteboard::obtenerPorId($targetId, $conexion);
                if (!$whiteboardToUpdate) {
                    throw new Exception('El whiteboard a sobrescribir no fue encontrado.');
                }

                $whiteboardToUpdate->setName($name);
                $whiteboardToUpdate->setImage_data($image_data);
                $whiteboardToUpdate->setWidth($width);
                $whiteboardToUpdate->setHeight($height);

                $success = $whiteboardToUpdate->modificar($conexion);
            } else {
                // Update existing whiteboard by name (original behavior)
                $existingWhiteboard->setImage_data($image_data);
                $existingWhiteboard->setWidth($width);
                $existingWhiteboard->setHeight($height);

                $success = $existingWhiteboard->modificar($conexion);
            }

            if ($success) {
                // For overwrite operations, return JSON response to maintain canvas state
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Whiteboard actualizado exitosamente',
                    'whiteboard_id' => $targetId ?: $existingWhiteboard->getId(),
                    'whiteboard_name' => $name
                ]);
                exit();
            } else {
                throw new Exception('Error al actualizar el whiteboard.');
            }
        } else {
            // Create new whiteboard
            $newwhiteboard->setName($name);
            $newwhiteboard->setImage_data($image_data);
            $newwhiteboard->setWidth($width);
            $newwhiteboard->setHeight($height);

            $whiteboard_id = $newwhiteboard->crear($conexion);

            if ($whiteboard_id) {
                header('Location: lwhiteboards?c=1');
                exit();
            } else {
                throw new Exception('Error al guardar el whiteboard.');
            }
        }

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_save_whiteboard

#region sub_load_whiteboard
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_load_whiteboard'])) {
    try {
        $whiteboard_id = (int)($_POST['whiteboard_id'] ?? 0);
        
        if ($whiteboard_id <= 0) {
            throw new Exception('ID de whiteboard no válido.');
        }

        $loaded_whiteboard = Whiteboard::obtenerPorId($whiteboard_id, $conexion);
        
        if (!$loaded_whiteboard) {
            throw new Exception('Whiteboard no encontrado.');
        }

        // Return JSON response for AJAX
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'data' => [
                'id'         => $loaded_whiteboard->getId(),
                'name'       => $loaded_whiteboard->getName(),
                'image_data' => $loaded_whiteboard->getImage_data(),
                'width'      => $loaded_whiteboard->getWidth(),
                'height'     => $loaded_whiteboard->getHeight()
            ]
        ]);
        exit();

    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
        exit();
    }
}
#endregion sub_load_whiteboard

#region sub_delete_whiteboard
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delete_whiteboard'])) {
    try {
        $whiteboard_id = (int)($_POST['whiteboard_id'] ?? 0);
        
        if ($whiteboard_id <= 0) {
            throw new Exception('ID de whiteboard no válido.');
        }

        $deleted = Whiteboard::eliminar($whiteboard_id, $conexion);
        
        if ($deleted) {
            header('Location: lwhiteboards?d=1');
            exit();
        } else {
            throw new Exception('Error al eliminar el whiteboard.');
        }

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delete_whiteboard

#region sub_search_whiteboards
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_search_whiteboards'])) {
    try {
        $searchTerm = limpiar_datos($_POST['search_term'] ?? '');

        if (empty($searchTerm)) {
            $searchResults = Whiteboard::obtenerTodos($conexion);
        } else {
            $searchResults = Whiteboard::buscarPorNombre($searchTerm, $conexion);
        }

        // Return JSON response for AJAX
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'data' => array_map(function($whiteboard) {
                return [
                    'id' => $whiteboard->getId(),
                    'name' => $whiteboard->getName(),
                    'width' => $whiteboard->getWidth(),
                    'height' => $whiteboard->getHeight(),
                    'created_at' => $whiteboard->getCreated_at()
                ];
            }, $searchResults)
        ]);
        exit();

    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
        exit();
    }
}
#endregion sub_search_whiteboards

#region try
try {
    $whiteboards = Whiteboard::obtenerTodos($conexion);
} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();

    // If table doesn't exist, provide helpful message
    if (strpos($e->getMessage(), "doesn't exist") !== false || strpos($e->getMessage(), "Table") !== false) {
        $error_text = "La tabla 'whiteboards' no existe en la base de datos. Por favor, ejecute el script 'database_setup_whiteboards.sql' para crear la tabla.";
    }

    // Initialize empty array to prevent view errors
    $whiteboards = [];
}
#endregion try

require_once __ROOT__ . '/views/lwhiteboards.view.php';

?>
