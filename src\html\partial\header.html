<!-- BEGIN #header -->@@if(context.theme == 'default') {
		<div id="header" class="app-header@@if(context.appHeaderInverse) { app-header-inverse}">
			<!-- BEGIN navbar-header -->
			<div class="navbar-header">@@if(context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-end-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}
				<a href="index.html" class="navbar-brand"><span class="navbar-logo"></span> <b>Color</b> Admin</a>@@if(context.pageWithMegaMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-bs-toggle="collapse" data-bs-target="#top-navbar">
					<span class="fa-stack fa-lg">
						<i class="far fa-square fa-stack-2x"></i>
						<i class="fa fa-cog fa-stack-1x mt-1px"></i>
					</span>
				</button>}@@if(context.pageWithMixedMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-top-menu-mobile">
					<span class="fa-stack fa-lg">
						<i class="far fa-square fa-stack-2x"></i>
						<i class="fa fa-cog fa-stack-1x mt-1px"></i>
					</span>
				</button>}@@if(context.pageWithTopMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-top-menu-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}@@if(!context.pageWithoutSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}
			</div>
			<!-- END navbar-header -->@@if(context.pageWithMegaMenu) {
			@@include('header/mega-menu.html')
			}
			<!-- BEGIN header-nav -->
			<div class="navbar-nav">
				<div class="navbar-item navbar-form">
					<form action="" method="POST" name="search">
						<div class="form-group">
							<input type="text" class="form-control" placeholder="Enter keyword" />
							<button type="submit" class="btn btn-search"><i class="fa fa-search"></i></button>
						</div>
					</form>
				</div>
				<div class="navbar-item dropdown">
					<a href="#" data-bs-toggle="dropdown" class="navbar-link dropdown-toggle icon">
						<i class="fa fa-bell"></i>
						<span class="badge">5</span>
					</a>
					@@include('header/dropdown-notification.html')
				</div>
				@@if(context.languageBar) {@@include('header/language-bar.html')}
				<div class="navbar-item navbar-user dropdown">
					<a href="#" class="navbar-link dropdown-toggle d-flex align-items-center" data-bs-toggle="dropdown">
						<img src="../assets/img/user/user-13.jpg" alt="" /> 
						<span>
							<span class="d-none d-md-inline">Adam Schwartz</span>
							<b class="caret"></b>
						</span>
					</a>
					@@include('header/dropdown-profile.html')
				</div>@@if(context.pageWithTwoSidebar) {
				<div class="navbar-divider d-none d-md-block"></div>
				<div class="navbar-item d-none d-md-block">
					<a href="javascript:;" data-toggle="app-sidebar-end" class="navbar-link icon">
						<i class="fa fa-th"></i>
					</a>
				</div>}
			</div>
			<!-- END header-nav -->
		</div>
	}@@if(context.theme == 'material') {
		<div id="header" class="app-header@@if(context.appHeaderInverse) { app-header-inverse}">
			<!-- BEGIN navbar-header -->
			<div class="navbar-header">@@if(!context.pageWithoutSidebar) {
				<button type="button" class="navbar-desktop-toggler" data-toggle="app-sidebar-minify">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}
				<a href="index.html" class="navbar-brand">
					Color Admin Material
				</a>@@if(context.pageWithMegaMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler navbar-mobile-toggler-end" data-bs-toggle="collapse" data-bs-target="#top-navbar">
					<span class="fa-stack fa-lg">
						<i class="far fa-square fa-stack-2x"></i>
						<i class="fa fa-cog fa-stack-1x mt-1px"></i>
					</span>
				</button>}@@if(context.pageWithMixedMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler navbar-mobile-toggler-end" data-toggle="app-top-menu-mobile">
					<span class="fa-stack fa-lg">
						<i class="far fa-square fa-stack-2x"></i>
						<i class="fa fa-cog fa-stack-1x mt-1px"></i>
					</span>
				</button>}@@if(context.pageWithTopMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler navbar-mobile-toggler-end" data-toggle="app-top-menu-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}
			</div>
			<!-- END navbar-header -->@@if(context.pageWithMegaMenu) {
			@@include('header/mega-menu.html')
			}
			<!-- BEGIN header-nav -->
			<div class="navbar-nav">
				@@if(context.languageBar) {
				@@include('header/language-bar.html')
				}<div class="navbar-item">
					<a href="#" data-toggle="app-header-floating-form" class="navbar-link icon">
						<i class="material-icons">search</i>
					</a>
				</div>
				<div class="navbar-item dropdown">
					<a href="#" data-bs-toggle="dropdown" class="navbar-link dropdown-toggle icon">
						<i class="material-icons">inbox</i>
						<span class="badge">5</span>
					</a>
					@@include('header/dropdown-notification.html')
				</div>
				<div class="navbar-item navbar-user dropdown">
					<a href="#" class="navbar-link dropdown-toggle d-flex" data-bs-toggle="dropdown">
						<span class="d-none d-md-inline">Hi, John Smith</span>
						<img src="../assets/img/user/user-14.jpg" alt="" /> 
					</a>
					@@include('header/dropdown-profile.html')
				</div>@@if(context.pageWithTwoSidebar) {
				<div class="navbar-divider d-none d-md-block"></div>
				<div class="navbar-item d-none d-md-block">
					<a href="javascript:;" data-toggle="app-sidebar-end" class="navbar-link icon">
						<i class="material-icons">apps</i>
					</a>
				</div>}
			</div>
			<!-- END header navigation right -->
			
			<div class="navbar-floating-form">
				<button class="search-btn" type="submit"><i class="material-icons">search</i></button>
				<input type="text" class="form-control" placeholder="Search Something..." />
				<a href="#" class="close" data-dismiss="app-header-floating-form"><i class="material-icons">close</i></a>
			</div>
		</div>
	}@@if(context.theme == 'apple') {
		<div id="header" class="app-header@@if(context.appHeaderInverse) { app-header-inverse}">
			<!-- BEGIN navbar-header -->
			<div class="navbar-header">@@if(context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-end-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}
				<a href="index.html" class="navbar-brand"><span class="navbar-logo"><i class="ion-ios-cloud"></i></span> <b class="me-1">Color</b> Admin</a>@@if(context.pageWithMegaMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-bs-toggle="collapse" data-bs-target="#top-navbar">
					<span class="fa-stack fa-lg">
						<i class="far fa-square fa-stack-2x"></i>
						<i class="fa fa-cog fa-stack-1x mt-1px"></i>
					</span>
				</button>}@@if(context.pageWithMixedMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-top-menu-mobile">
					<span class="fa-stack fa-lg">
						<i class="far fa-square fa-stack-2x"></i>
						<i class="fa fa-cog fa-stack-1x mt-1px"></i>
					</span>
				</button>}@@if(context.pageWithTopMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-top-menu-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}@@if(!context.pageWithoutSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}
			</div>
			<!-- END navbar-header -->@@if(context.pageWithMegaMenu) {
			@@include('header/mega-menu.html')
			}
			<!-- BEGIN header-nav -->
			<div class="navbar-nav">
				<div class="navbar-item navbar-form">
					<form action="" method="POST" name="search">
						<div class="form-group">
							<input type="text" class="form-control" placeholder="Enter keyword" />
							<button type="submit" class="btn btn-search"><i class="ion-ios-search"></i></button>
						</div>
					</form>
				</div>
				<div class="navbar-item dropdown">
					<a href="#" data-bs-toggle="dropdown" class="navbar-link dropdown-toggle icon">
						<i class="ion-ios-notifications"></i>
						<span class="badge">5</span>
					</a>
					@@include('header/dropdown-notification.html')
				</div>@@if(context.languageBar) {
				@@include('header/language-bar.html')
				}
				<div class="navbar-item navbar-user dropdown">
					<a href="#" class="navbar-link dropdown-toggle d-flex align-items-center" data-bs-toggle="dropdown">
						<img src="../assets/img/user/user-13.jpg" alt="" /> 
						<span>
							<span class="d-none d-md-inline">Adam Schwartz</span>
							<b class="caret"></b>
						</span>
					</a>
					@@include('header/dropdown-profile.html')
				</div>@@if(context.pageWithTwoSidebar) {
				<div class="navbar-divider d-none d-md-block"></div>
				<div class="navbar-item d-none d-md-block">
					<a href="javascript:;" data-toggle="app-sidebar-end" class="navbar-link icon">
						<i class="ion-ios-keypad"></i>
					</a>
				</div>}
			</div>
			<!-- END header-nav -->
		</div>
	}@@if(context.theme == 'transparent') {
		<div id="header" class="app-header@@if(context.showBg) { app-header-show-bg}">
			<!-- BEGIN navbar-header -->
			<div class="navbar-header">@@if(context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-end-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}
				<a href="index.html" class="navbar-brand"><span class="navbar-logo"></span> <b class="me-1">Color</b> Admin</a>@@if(context.pageWithMegaMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-bs-toggle="collapse" data-bs-target="#top-navbar">
					<span class="fa-stack fa-lg">
						<i class="far fa-square fa-stack-2x"></i>
						<i class="fa fa-cog fa-stack-1x mt-1px"></i>
					</span>
				</button>}@@if(context.pageWithMixedMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-top-menu-mobile">
					<span class="fa-stack fa-lg">
						<i class="far fa-square fa-stack-2x"></i>
						<i class="fa fa-cog fa-stack-1x mt-1px"></i>
					</span>
				</button>}@@if(context.pageWithTopMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-top-menu-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}@@if(!context.pageWithoutSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}
			</div>
			<!-- END navbar-header -->@@if(context.pageWithMegaMenu) {
			@@include('header/mega-menu.html')
			}
			<!-- BEGIN header-nav -->
			<div class="navbar-nav">
				<div class="navbar-item navbar-form">
					<form action="" method="POST" name="search">
						<div class="form-group">
							<input type="text" class="form-control" placeholder="Enter keyword" />
							<button type="submit" class="btn btn-search"><i class="fa fa-search"></i></button>
						</div>
					</form>
				</div>
				<div class="navbar-item dropdown">
					<a href="#" data-bs-toggle="dropdown" class="navbar-link dropdown-toggle icon">
						<i class="fa fa-bell"></i>
						<span class="badge">5</span>
					</a>
					@@include('header/dropdown-notification.html')
				</div>@@if(context.languageBar) {
				@@include('header/language-bar.html')
				}
				<div class="navbar-item navbar-user dropdown">
					<a href="#" class="navbar-link dropdown-toggle d-flex align-items-center" data-bs-toggle="dropdown">
						<img src="../assets/img/user/user-13.jpg" alt="" /> 
						<span>
							<span class="d-none d-md-inline">Adam Schwartz</span>
							<b class="caret"></b>
						</span>
					</a>
					@@include('header/dropdown-profile.html')
				</div>@@if(context.pageWithTwoSidebar) {
				<div class="navbar-divider d-none d-md-block"></div>
				<div class="navbar-item d-none d-md-block">
					<a href="javascript:;" data-toggle="app-sidebar-end" class="navbar-link icon">
						<i class="fa fa-th"></i>
					</a>
				</div>}
			</div>
			<!-- END header-nav -->
		</div>
	}@@if(context.theme == 'facebook') {
		<div id="header" class="app-header app-header-inverse">
			<!-- BEGIN navbar-header -->
			<div class="navbar-header">@@if(context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-end-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}
				<a href="index.html" class="navbar-brand"><i class="fab fa-facebook-square fa-lg"></i> <b>Color</b> Admin <small>social</small></a>@@if(context.pageWithMegaMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-bs-toggle="collapse" data-bs-target="#top-navbar">
					<span class="fa-stack fa-lg">
						<i class="far fa-square fa-stack-2x"></i>
						<i class="fa fa-cog fa-stack-1x mt-1px"></i>
					</span>
				</button>}@@if(context.pageWithMixedMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-top-menu-mobile">
					<span class="fa-stack fa-lg">
						<i class="far fa-square fa-stack-2x"></i>
						<i class="fa fa-cog fa-stack-1x mt-1px"></i>
					</span>
				</button>}@@if(context.pageWithTopMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-top-menu-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}@@if(!context.pageWithoutSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}
			</div>
			<!-- END navbar-header -->@@if(context.pageWithMegaMenu) {
			@@include('header/mega-menu.html')
			}
			<!-- BEGIN header-nav -->
			<div class="navbar-nav">
				<div class="navbar-item navbar-form">
					<form action="" method="POST" name="search">
						<div class="form-group">
							<input type="text" class="form-control" placeholder="Enter keyword" />
							<button type="submit" class="btn btn-search"><i class="fa fa-search"></i></button>
						</div>
					</form>
				</div>
				<div class="navbar-item dropdown">
					<a href="#" data-bs-toggle="dropdown" class="navbar-link dropdown-toggle icon">
						<i class="fa fa-bell"></i>
						<span class="badge">5</span>
					</a>
					@@include('header/dropdown-notification.html')
				</div>@@if(context.languageBar) {
				@@include('header/language-bar.html')
				}
				<div class="navbar-item navbar-user dropdown">
					<a href="#" class="navbar-link dropdown-toggle d-flex align-items-center" data-bs-toggle="dropdown">
						<img src="../assets/img/user/user-13.jpg" alt="" /> 
						<span class="d-none d-md-inline">Adam Schwartz</span> <b class="caret ms-6px"></b>
					</a>
					@@include('header/dropdown-profile.html')
				</div>@@if(context.pageWithTwoSidebar) {
				<div class="navbar-divider d-none d-md-block"></div>
				<div class="navbar-item d-none d-md-block">
					<a href="javascript:;" data-toggle="app-sidebar-end" class="navbar-link fs-14px">
						<i class="fa fa-th"></i>
					</a>
				</div>}
			</div>
			<!-- END header-nav -->
		</div>
	}@@if(context.theme == 'google') {
		<div id="header" class="app-header@@if(context.appHeaderInverse) { app-header-inverse}">
			<!-- BEGIN navbar-header -->
			<div class="navbar-header">@@if(!context.pageWithoutSidebar) {
				<button type="button" class="navbar-desktop-toggler" data-toggle="app-sidebar-minify">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}@@if(context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-end-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}
				<a href="index.html" class="navbar-brand">
					<b class="me-1">Color</b> Admin
					<span class="navbar-logo">
						<span class="text-blue">G</span>
						<span class="text-red">o</span>
						<span class="text-orange">o</span>
						<span class="text-blue">g</span>
						<span class="text-green">l</span>
						<span class="text-red">e</span>
					</span>
				</a>@@if(context.pageWithMegaMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-bs-toggle="collapse" data-bs-target="#top-navbar">
					<span class="fa-stack fa-lg">
						<i class="far fa-square fa-stack-2x"></i>
						<i class="fa fa-cog fa-stack-1x mt-1px"></i>
					</span>
				</button>}@@if(context.pageWithMixedMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-top-menu-mobile">
					<span class="fa-stack fa-lg">
						<i class="far fa-square fa-stack-2x"></i>
						<i class="fa fa-cog fa-stack-1x mt-1px"></i>
					</span>
				</button>}@@if(context.pageWithTopMenu && !context.pageWithTwoSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-top-menu-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}@@if(!context.pageWithoutSidebar) {
				<button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-mobile">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>}
			</div>
			<!-- END navbar-header -->@@if(context.pageWithMegaMenu) {
			@@include('header/mega-menu.html')
			}
			<!-- BEGIN header-nav -->
			<div class="navbar-nav">
				<div class="navbar-item navbar-form">
					<form action="" method="POST" name="search">
						<div class="form-group">
							<input type="text" class="form-control" placeholder='Try searching "Users Today"' />
							<button type="submit" class="btn btn-search"><i class="fa fa-search"></i></button>
						</div>
					</form>
				</div>
				<div class="navbar-item dropdown">
					<a href="#" data-bs-toggle="dropdown" class="navbar-link dropdown-toggle icon">
						<i class="fa fa-bell"></i>
						<span class="badge">5</span>
					</a>
					@@include('header/dropdown-notification.html')
				</div>@@if(context.languageBar) {
				@@include('header/language-bar.html')
				}
				<div class="navbar-item navbar-user dropdown">
					<a href="#" class="navbar-link dropdown-toggle d-flex align-items-center" data-bs-toggle="dropdown">
						<img src="../assets/img/user/user-13.jpg" alt="" /> 
						<span class="d-none d-md-inline">Adam Schwartz</span> <b class="caret ms-lg-2"></b>
					</a>
					@@include('header/dropdown-profile.html')
				</div>@@if(context.pageWithTwoSidebar) {
				<div class="navbar-divider d-none d-md-block"></div>
				<div class="navbar-item d-none d-md-block">
					<a href="javascript:;" data-toggle="app-sidebar-end" class="navbar-link fs-14px">
						<i class="fa fa-th"></i>
					</a>
				</div>}
			</div>
			<!-- END header-nav -->
		</div>
	}	<!-- END #header -->
	