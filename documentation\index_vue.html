<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<head lang="en">
	<meta http-equiv="content-type" content="text/html;charset=utf-8">
	<title> Documentation - Color Admin</title>
	<!-- Bootstrap styles -->
	<link href="assets/bootstrap/css/bootstrap.css" rel="stylesheet">
	<link href="assets/bootstrap/css/bootstrap-responsive.css" rel="stylesheet">
	<link href="assets/bootstrap/css/docs.css" rel="stylesheet">
	<link href="assets/bootstrap/js/google-code-prettify/prettify.css" rel="stylesheet">

	<!-- Le HTML5 shim, for IE6-8 support of HTML5 elements -->
	<!--[if lt IE 9]>
		<script src="assets/js/html5shiv.js"></script>
	<![endif]-->
</head>
<body data-spy="scroll" data-target=".bs-docs-sidebar">
	<div class="navbar navbar-inverse navbar-page">
		<div class="navbar-inner">
			<div class="container">
				<button type="button" class="btn btn-navbar collapsed" data-toggle="collapse" data-target=".nav-collapse">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>
				<a class="brand" href="#">Admin Template</a>
				<div class="nav-collapse collapse">
					<ul class="nav">
						<li class="">
							<a href="index.html">Design Template</a>
						</li>
						<li class="">
							<a href="index_ajax.html">Ajax Version</a>
						</li>
						<li class="">
							<a href="index_angular_1x.html">Angular 1.x</a>
						</li>
						<li class="">
							<a href="index_angular_13.html">Angular 13.0</a>
						</li>
						<li class="">
							<a href="index_laravel.html">Laravel Version</a>
						</li>
						<li class="active">
							<a href="index_vue.html">Vue Version</a>
						</li>
						<li class="">
							<a href="index_react.html">React Version</a>
						</li>
						<li class="">
							<a href="index_asp.html">ASP.NET</a>
						</li>
						<li>
							<a href="index_change_log.html">Change Log</a>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
	<header class="jumbotron subhead" id="overview">
		<div class="container">
			<h1 class="text-center">Color Admin</h1>
			<p class="lead text-center">&ldquo;Vue JS Version&rdquo; Documentation by &ldquo;Sean Ngu&rdquo; v5.1.4</p>
		</div>
		<div class="jumbotron-cover"></div>
	</header>
	<div class="container">
		<div class="row">
			<div class="span12">
				<div class="well with-cover">
					<div class="well-cover" style="background-image: url(assets/images/vue.jpg); background-size: auto 80%; background-position: center; background-repeat: no-repeat; background-color: #fff;"><span style="position: absolute; bottom: 25px; right: 70px; font-weight: bold;">CLI</span></div>
					<p>
						<strong>
							Last Updated: 13/February/2022<br>
							By: Sean Ngu<br>
							Email: <a href="mailto:<EMAIL>"><EMAIL></a>
						</strong>
					</p>
					<p>
						Thank you for purchasing my theme. If you have any questions that are beyond the scope of this help file,
						please feel free to email your question to my email <a href="mailTo:<EMAIL>"><EMAIL></a>. Thanks so much!
					</p>
			
				</div>
			</div><!-- end span12 -->
		</div><!-- end row -->
		<div class="row">
			<div class="span3 bs-docs-sidebar">
				<ul class="nav nav-list bs-docs-sidenav affix-top">
					<li><a href="#installation"><i class="icon-chevron-right"></i>Installation</a></li>
					<li><a href="#fileStructure"><i class="icon-chevron-right"></i>File Structure</a></li>
					<li><a href="#page-structure"><i class="icon-chevron-right"></i>Page Structure</a></li>
					<li><a href="#page-components"><i class="icon-chevron-right"></i>Components</a></li>
					<li><a href="#page-options"><i class="icon-chevron-right"></i>Page Options</a></li>
					<li><a href="#page-dark-mode"><i class="icon-chevron-right"></i>Dark Mode <span class="label">NEW</span></a></li>
					<li><a href="#page-switch-design"><i class="icon-chevron-right"></i>Switch Design <span class="label">NEW</span></a></li>
					<li><a href="#page-scss"><i class="icon-chevron-right"></i>SCSS</a></li>
					<li><a href="#npm-package"><i class="icon-chevron-right"></i>NPM Package</a></li>
				</ul>
			</div>
			<div class="span9">
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="installation"><strong>A) Installation</strong> - <a href="#top">top</a></h3>
						</div>
						<p>
							Follow the following step to install the vue cli in your localhost<br />
							You may refer to their official documentation for how to setup the development environment. <br />
							<a href="https://cli.vuejs.org/guide/installation.html" target="_blank">Setup Guide</a>
						</p>
<pre class="prettyprint linenums">
&lt;!-- run the following command --&gt;
cd /your-path-url/template_vuejs
npm install --force
npm run serve

&lt;!-- browse the url --&gt;
http://localhost:8081/
</pre>
						<p>
							Verify that you are running at least node <code>16.13.x</code> or later and <code>npm 8.x.x</code> by running <code>node -v</code> and <code>npm -v</code> in a terminal/console window. Older versions produce errors, but newer versions are fine.
						</p>
						<hr />
						<p>Copy over the required image from global <code>assets</code> folder</p>
<pre class="prettyprint linenums">
&lt;!-- copy the following folder--&gt;
/admin/template/assets/img
 
&lt;!-- paste it into vuejs folder --&gt;
/admin/template/template_vuejs/public/assets/img
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="fileStructure"><strong>B) File Structure</strong> - <a href="#top">top</a></h3>
						</div>
						<p>File structure overview for Vue JS Version</p>
						
<pre class="prettyprint linenums">
template_vue/
├── package.json
├── babel.conf.js
├── public/
└── src/
    ├── App.vue
    ├── main.js
    ├── assets/
    ├── components/
    ├── config/
    ├── pages/
    ├── plugins/
    └── scss/
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-structure"><strong>C) Page Structure</strong> - <a href="#top">top</a></h3>
						</div>
						<p>Below is the code from <code>App.vue</code> which include the header, sidebar, right sidebar, top menu, page content and footer. You may remove the component if you are not using it.</p>
<pre class="prettyprint linenums">
&lt;template&gt;
  &lt;div class="app" v-if="!appOptions.appEmpty" v-bind:class="{
    'app-header-fixed': appOptions.appHeaderFixed && !appOptions.appHeaderNone,
    'app-sidebar-fixed': appOptions.appSidebarFixed,
    'app-sidebar-minified': appOptions.appSidebarMinified, 
    'app-content-full-height': appOptions.appContentFullHeight, 
    'app-without-sidebar': appOptions.appSidebarNone, 
    'app-with-end-sidebar': appOptions.appSidebarEnd, 
    'app-with-two-sidebar': appOptions.appSidebarTwo,
    'app-with-wide-sidebar': appOptions.appSidebarWide,
    'app-with-light-sidebar': appOptions.appSidebarLight,
    'app-sidebar-mobile-toggled': appOptions.appSidebarMobileToggled,
    'app-sidebar-end-toggled': appOptions.appSidebarEndToggled,
    'app-sidebar-end-collapsed': !appOptions.appSidebarEndToggled,
    'app-sidebar-end-mobile-toggled': appOptions.appSidebarEndMobileToggled,
    'app-without-header': appOptions.appHeaderNone,
    'app-with-top-menu': appOptions.appTopMenu,
    'app-gradient-enabled': appOptions.appGradientEnabled,
    'has-scroll': appOptions.appBodyScrollTop
  }"&gt;
    &lt;Header /&gt;
    &lt;TopMenu v-if="appOptions.appTopMenu" /&gt;
    &lt;Sidebar v-if="!appOptions.appSidebarNone" /&gt;
    &lt;SidebarRight v-if="appOptions.appSidebarTwo" /&gt;
    &lt;div id="content" class="app-content" v-bind:class="appOptions.appContentClass"&gt;
      &lt;router-view&gt;&lt;/router-view&gt;
      &lt;vue-ins-progress-bar&gt;&lt;/vue-ins-progress-bar&gt;
    &lt;/div&gt;
    &lt;SidebarRight v-if="appOptions.appSidebarTwo" /&gt;
    &lt;ThemePanel v-on:change-theme="handleChangeTheme" /&gt;
    &lt;ScrollTopBtn /&gt;
  &lt;/div&gt;
  &lt;div class="h-100" v-else&gt;
    &lt;router-view&gt;&lt;/router-view&gt;
    &lt;vue-ins-progress-bar&gt;&lt;/vue-ins-progress-bar&gt;
  &lt;/div&gt;
&lt;/template&gt;
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-components"><strong>D) Components</strong> - <a href="#top">top</a></h3>
						</div>
						<p>List of components inside the components folder</p>
<pre class="prettyprint linenums">
components/
├── header
├── sidebar/
├── sidebar-right/
├── top-menu/
└── vue-chartjs/
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-options"><strong>E) Page Options</strong> - <a href="#top">top</a></h3>
						</div>
						<p>File to configure the default page options & page routes</p>
<pre class="prettyprint linenums">
config/
├── AppOptions.vue
└── AppRoutes.vue
</pre>
						<p>Example of how to change page options in single page</p>
<pre class="prettyprint linenums">
&lt;script&gt;
import AppOptions from '../config/AppOptions.vue'

export default {
  created() {
    AppOptions.appTopMenu = true;
    AppOptions.appSidebarNone = true;
  },
  beforeRouteLeave (to, from, next) {
    // change back to default
    AppOptions.appTopMenu = false;
    AppOptions.appSidebarNone = false;
    next();
  }
}
&lt;/script&gt;
</pre>
						<p>List of options:</p>
<pre class="prettyprint linenums">
&lt;script&gt;
const appOptions = {
  appTheme: 'teal',
  appDarkMode: false,
  appGradientEnabled: false,
  appSidebarMinified: false,
  appSidebarNone: false,
  appSidebarEnd: false,
  appSidebarTwo: false,
  appSidebarWide: false,
  appSidebarLight: false,
  appSidebarTransparent: false,
  appSidebarMobileToggled: false,
  appSidebarEndMobileToggled: false,
  appSidebarEndToggled: false,
  appSidebarEndCollapsed: false,
  appSidebarSearch: false,
  appSidebarFixed: true,
  appSidebarGrid: false,
  appContentFullHeight: false,
  appContentClass: false,
  appHeaderLanguageBar: false,
  appHeaderInverse: false,
  appHeaderMegaMenu: false,
  appHeaderFixed: true,
  appHeaderMegaMenuMobileToggled: false,
  appTopMenu: false,
  appTopMenuMobileToggled: false,
  appEmpty: false,
  appBodyScrollTop: 0
}

export default appOptions;
&lt;/script&gt;
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-dark-mode"><strong>F) Dark Mode</strong> - <a href="#top">top</a></h3>
						</div>
						<p>Enable the dark mode from <code>template_vue/config/AppOptions.vue</code></p>
<pre class="prettyprint linenums">
...
appDarkMode: true
...
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-switch-design"><strong>G) Switch Design</strong> - <a href="#top">top</a></h3>
						</div>
						<h4 style="margin-bottom: 15px">Apple Design</h4>
						<ol>
							<li>
								<div style="padding-bottom: 5px;">Change the variable from <code>template_vue/src/scss/vue.scss</code>.</div>							
<pre class="prettyprint linenums">
@import 'apple/styles';
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px;">Add the following css link to <code>template_vue/public/index.html</code>.</div>							
<pre class="prettyprint linenums">
&lt;link href="https://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css" rel="stylesheet" /&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Change the sidebar icon from Fontawesome to Ionicons and background color class is needed as well.</div>
<pre class="prettyprint linenums">
&lt;div class="menu-icon"&gt;
  &lt;i class="ion-ios-pulse bg-gradient-blue"&gt;&lt;/i&gt;
&lt;/div&gt;
</pre>
							</li>
						</ol>
						<hr style="margin-top: 30px" />
						<h4 style="margin-bottom: 15px">Facebook Design</h4>
						<ol>
							<li>
								<div style="padding-bottom: 5px;">Change the variable from <code>template_vue/src/scss/vue.scss</code>.</div>							
<pre class="prettyprint linenums">
@import 'facebook/styles';
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Enable the app header inverse from <code>template_vue/config/AppOptions.vue</code>.</div>
<pre class="prettyprint linenums">
...
appHeaderInverse: true
...
</pre>
							</li>
						</ol>
						<hr style="margin-top: 30px" />
						<h4 style="margin-bottom: 15px">Transparent Design</h4>
						<ol>
							<li>
								<div style="padding-bottom: 5px;">Change the variable from <code>template_vue/src/scss/vue.scss</code>.</div>							
<pre class="prettyprint linenums">
@import 'transparent/styles';
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Add the <code>.app-cover</code> next to the <code>&lt;body&gt;</code> tag in <code>template_vue/public/index.html</code>.</div>
<pre class="prettyprint linenums">
&lt;body&gt;
  &lt;!-- BEGIN page-cover --&gt;
  &lt;div class="app-cover"&gt;&lt;/div&gt;
  &lt;!-- END page-cover --&gt;
  
  ...
&lt;/body&gt;
</pre>
							</li>
						</ol>
						<hr style="margin-top: 30px" />
						<h4 style="margin-bottom: 15px">Google Design</h4>
						<ol>
							<li>
								<div style="padding-bottom: 5px;">Change the variable from <code>template_vue/src/scss/vue.scss</code>.</div>							
<pre class="prettyprint linenums">
@import 'google/styles';
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px;">Add the following css link to <code>template_vue/public/index.html</code>.</div>							
<pre class="prettyprint linenums">
&lt;link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900" rel="stylesheet" /&gt;
&lt;link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" /&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Change the sidebar icon from Fontawesome to Material Icons.</div>
<pre class="prettyprint linenums">
&lt;div class="menu-icon"&gt;
  &lt;i class="material-icons"&gt;home&lt;/i&gt;
&lt;/div&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Enable the app sidebar light & wide option from <code>template_vue/config/AppOptions.vue</code>.</div>
<pre class="prettyprint linenums">
...
appSidebarWide: true,
appSidebarLight: true,
...
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Add the navbar desktop toggler to the <code>.app-header</code>  in <code>template_vue/src/components/header/Header.vue</code>.</div>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #header --&gt;
&lt;div id="header" class="app-header"&gt;
  &lt;!-- BEGIN navbar-header --&gt;
  &lt;div class="navbar-header"&gt;
    &lt;button type="button" class="navbar-desktop-toggler"&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
    &lt;/button&gt;
    &lt;button type="button" class="navbar-mobile-toggler"&gt;
      ...
    &lt;/button&gt;
    &lt;a class="navbar-brand"&gt;
      Color Admin
    &lt;/a&gt;
  &lt;/div&gt;
  &lt;!-- END navbar-header --&gt;
  ...
&lt;/div&gt;
</pre>
							</li>
						</ol>
						<hr style="margin-top: 30px" />
						<h4 style="margin-bottom: 15px">Material Design</h4>
						<ol>
							<li>
								<div style="padding-bottom: 5px;">Change the variable from <code>template_vue/src/scss/vue.scss</code>.</div>							
<pre class="prettyprint linenums">
@import 'material/styles';
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px;">Add the following css link to <code>template_vue/public/index.html</code>.</div>							
<pre class="prettyprint linenums">
&lt;link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900" rel="stylesheet" /&gt;
&lt;link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" /&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Change the sidebar icon from Fontawesome to Material Icons.</div>
<pre class="prettyprint linenums">
&lt;div class="menu-icon"&gt;
  &lt;i class="material-icons"&gt;home&lt;/i&gt;
&lt;/div&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Enable the app sidebar wide option from <code>template_vue/config/AppOptions.vue</code>.</div>
<pre class="prettyprint linenums">
...
appSidebarWide: true,
...
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Add the navbar desktop toggler to the <code>.app-header</code>  in <code>template_vue/src/components/header/Header.vue</code>.</div>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #header --&gt;
&lt;div id="header" class="app-header"&gt;
  &lt;!-- BEGIN navbar-header --&gt;
  &lt;div class="navbar-header"&gt;
    &lt;button type="button" class="navbar-desktop-toggler"&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
    &lt;/button&gt;
    &lt;button type="button" class="navbar-mobile-toggler"&gt;
      ...
    &lt;/button&gt;
    &lt;a class="navbar-brand"&gt;
      Color Admin Material
    &lt;/a&gt;
  &lt;/div&gt;
  &lt;!-- END navbar-header --&gt;
  ...
&lt;/div&gt;
</pre>
							</li>
							
							<li>
								<div style="padding-bottom: 5px">Add the floating navbar form to the <code>.app-header</code>  in <code>template_vue/src/components/header/Header.vue</code> AND <b>REMOVE</b> the default <code>.navbar-form</code>.</div>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #header --&gt;
&lt;div id="header" class="app-header"&gt;
  &lt;!-- BEGIN header-nav --&gt;
  &lt;div class="navbar-nav"&gt;
    &lt;div class="navbar-item"&gt;
      &lt;a href="#" class="navbar-link icon"&gt;
        &lt;i class="material-icons"&gt;search&lt;/i&gt;
      &lt;/a&gt;
      
      &lt;!-- REMOVE IT --&gt;
      &lt;div class="navbar-item navbar-form"&gt;
        ...
      &lt;/div&gt;
    &lt;/div&gt;
    ...
  &lt;/div&gt;
  &lt;!-- END header-nav --&gt;
  
  &lt;div class="navbar-floating-form"&gt;
    &lt;button class="search-btn" type="submit"&gt;&lt;i class="material-icons"&gt;search&lt;/i&gt;&lt;/button&gt;
    &lt;input type="text" class="form-control" placeholder="Search Something..." /&gt;
    &lt;a href="#" class="close"&gt;
      &lt;i class="material-icons"&gt;close&lt;/i&gt;
    &lt;/a&gt;
  &lt;/div&gt;
&lt;/div&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Change the <code>.app-loader</code>  in <code>template_vue/public/index.html</code>.</div>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #loader --&gt;
&lt;div id="loader" class="app-loader"&gt;
  &lt;div class="material-loader"&gt;
    &lt;svg class="circular" viewBox="25 25 50 50"&gt;
      &lt;circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10"&gt;&lt;/circle&gt;
    &lt;/svg&gt;
    &lt;div class="message"&gt;Loading...&lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;
&lt;!-- END #loader --&gt;
</pre>
							</li>
						</ol>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-scss"><strong>H) Using SCSS</strong> - <a href="#top">top</a></h3>
						</div>
						<p>Now Vue.js version is fully scss configured. You may switch the Color Admin theme by changing the file <code>/template_vuejs/src/scss/vue.scss</code>.</p>
<pre class="prettyprint linenums">
@import 'default/styles';
 
&lt;!-- other themes --&gt;
@import 'apple/styles';
@import 'facebook/styles';
@import 'google/styles';
@import 'material/styles';
@import 'transparent/styles';
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="npm-package"><strong>I) NPM Package</strong> - <a href="#top">top</a></h3>
						</div>
						<p>
							Below is the list of package that has been installed in this project. You may use the following example to find the package from their official website.
							<code>https://www.npmjs.com/package/</code><code>bootstrap-vue</code>
						</p>
<pre class="prettyprint linenums">
{
  "name": "color-admin",
  "version": "0.0.0",
  "description": "",
  "private": true,
  "scripts": {
    "serve": "vue-cli-service serve",
    "build": "vue-cli-service build",
    "lint": "vue-cli-service lint"
  },
  "dependencies": {
    "@agametov/vueditor": "^0.4.1",
    "@chenfengyuan/vue-countdown": "^1.1.5",
    "@fortawesome/fontawesome-free": "^6.0.0-beta2",
    "@fullcalendar/bootstrap": "^5.10.1",
    "@fullcalendar/core": "^5.10.1",
    "@fullcalendar/daygrid": "^5.10.1",
    "@fullcalendar/interaction": "^5.10.1",
    "@fullcalendar/list": "^5.10.1",
    "@fullcalendar/timegrid": "^5.10.1",
    "@fullcalendar/vue": "^5.10.1",
    "apexcharts": "^3.31.0",
    "bootstrap": "^5.1.3",
    "bootstrap-social": "^5.1.1",
    "bootstrap-vue": "^2.21.2",
    "chart.js": "^2.9.4",
    "core-js": "^3.19.2",
    "flag-icon-css": "^4.1.6",
    "ionicons": "^4.2.6",
    "moment": "^2.29.1",
    "node-sass": "^6.0.1",
    "sass-loader": "^10.1.1",
    "simple-line-icons": "^2.5.5",
    "v-autocomplete": "^1.8.2",
    "vue": "^2.6.14",
    "vue-apexcharts": "^1.6.2",
    "vue-bootstrap-datetimepicker": "^5.0.1",
    "vue-chartjs": "^3.5.1",
    "vue-custom-scrollbar": "^1.4.1",
    "vue-event-calendar": "^1.5.2",
    "vue-good-table": "^2.21.11",
    "vue-hljs": "^2.0.0",
    "vue-input-tag": "^2.0.7",
    "vue-ins-progress-bar": "^1.3.8",
    "vue-maskedinput": "^0.1.3",
    "vue-notification": "^1.3.20",
    "vue-nvd3": "^1.0.0",
    "vue-pop-colorpicker": "^1.0.2",
    "vue-router": "^3.5.3",
    "vue-select": "^3.16.0",
    "vue-slider-component": "^3.2.15",
    "vue-sparklines": "^0.2.1",
    "vue-sweetalert2": "^5.0.2",
    "vue2-daterange-picker": "^0.6.7",
    "vue2-google-maps": "^0.10.7",
    "vuejs-datepicker": "^1.6.2"
  },
  "devDependencies": {
    "@vue/cli-plugin-babel": "^4.5.15",
    "@vue/cli-plugin-eslint": "^4.5.15",
    "@vue/cli-service": "^4.5.15",
    "babel-eslint": "^10.1.0",
    "eslint": "^6.8.0",
    "eslint-plugin-vue": "^7.11.1",
    "vue-template-compiler": "^2.6.14"
  },
  "eslintConfig": {
    "root": true,
    "env": {
      "node": true
    },
    "extends": [
      "plugin:vue/essential",
      "eslint:recommended"
    ],
    "rules": {},
    "parserOptions": {
      "parser": "babel-eslint"
    }
  },
  "postcss": {
    "plugins": {
      "autoprefixer": {}
    }
  },
  "browserslist": [
    "> 1%",
    "last 2 versions",
    "not ie <= 8"
  ]
}
</pre>
					</div>
				</div><!-- end row-fluid -->
			</div><!-- end span12 -->
		</div><!-- end row-fluid -->
	</div><!-- end container -->
	
	<footer class="footer">
		<div class="container text-left">
			<p>Once again, thank you so much for purchasing this theme. As I said at the beginning, I'd be glad to help you if you have any questions relating to this theme. No guarantees, but I'll do my best to assist. If you have a more general question relating to the themes, you might consider visiting the forums and asking your question via <a href="mailTo:<EMAIL>">email</a>.</p> 
			<br />
			<p class="append-bottom alt large"><strong>Sean Ngu</strong></p>
			<p><a href="#top">Go To Table of Contents</a></p>
		</div>
	</footer><!-- end footer -->
	
	<script src="assets/bootstrap/js/jquery.js"></script>
	<script src="assets/bootstrap/js/bootstrap-transition.js"></script>
	<script src="assets/bootstrap/js/bootstrap-alert.js"></script>
	<script src="assets/bootstrap/js/bootstrap-modal.js"></script>
	<script src="assets/bootstrap/js/bootstrap-dropdown.js"></script>
	<script src="assets/bootstrap/js/bootstrap-scrollspy.js"></script>
	<script src="assets/bootstrap/js/bootstrap-tab.js"></script>
	<script src="assets/bootstrap/js/bootstrap-tooltip.js"></script>
	<script src="assets/bootstrap/js/bootstrap-popover.js"></script>
	<script src="assets/bootstrap/js/bootstrap-button.js"></script>
	<script src="assets/bootstrap/js/bootstrap-collapse.js"></script>
	<script src="assets/bootstrap/js/bootstrap-carousel.js"></script>
	<script src="assets/bootstrap/js/bootstrap-typeahead.js"></script>
	<script src="assets/bootstrap/js/bootstrap-affix.js"></script>

	<script src="assets/bootstrap/js/holder/holder.js"></script>
	<script src="assets/bootstrap/js/google-code-prettify/prettify.js"></script>
	<script src="assets/bootstrap/js/application.js"></script>
</body>
</html>