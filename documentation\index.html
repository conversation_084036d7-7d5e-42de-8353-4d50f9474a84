<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<head lang="en">
	<meta http-equiv="content-type" content="text/html;charset=utf-8">
	<title> Documentation - Color Admin</title>
	<!-- Bootstrap styles -->
	<link href="assets/bootstrap/css/bootstrap.css" rel="stylesheet">
	<link href="assets/bootstrap/css/bootstrap-responsive.css" rel="stylesheet">
	<link href="assets/bootstrap/css/docs.css" rel="stylesheet">
	<link href="assets/bootstrap/js/google-code-prettify/prettify.css" rel="stylesheet">

	<!-- Le HTML5 shim, for IE6-8 support of HTML5 elements -->
	<!--[if lt IE 9]>
		<script src="assets/js/html5shiv.js"></script>
	<![endif]-->
</head>
<body data-spy="scroll" data-target=".bs-docs-sidebar">
	<div class="navbar navbar-inverse navbar-page">
		<div class="navbar-inner">
			<div class="container">
				<button type="button" class="btn btn-navbar collapsed" data-toggle="collapse" data-target=".nav-collapse">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>
				<a class="brand" href="#">Admin Template</a>
				<div class="nav-collapse collapse">
					<ul class="nav">
						<li class="active">
							<a href="index.html">Design Template</a>
						</li>
						<li class="">
							<a href="index_ajax.html">Ajax Version</a>
						</li>
						<li class="">
							<a href="index_angular_1x.html">Angular 1.x</a>
						</li>
						<li class="">
							<a href="index_angular_13.html">Angular 13.0</a>
						</li>
						<li class="">
							<a href="index_laravel.html">Laravel Version</a>
						</li>
						<li class="">
							<a href="index_vue.html">Vue Version</a>
						</li>
						<li class="">
							<a href="index_react.html">React Version</a>
						</li>
						<li class="">
							<a href="index_asp.html">ASP.NET</a>
						</li>
						<li>
							<a href="index_change_log.html">Change Log</a>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
	<header class="jumbotron subhead" id="overview">
		<div class="container">
			<h1 class="text-center">Color Admin</h1>
			<p class="lead text-center">&ldquo;Default Flat Design&rdquo; Documentation by &ldquo;Sean Ngu&rdquo; v5.1.4</p>
			
			<ul class="template-list">
				<li class="active"><a href="index.html">Default Design</a></li>
				<li><a href="index_material.html">Material Design</a></li>
				<li><a href="index_apple.html">Apple Design</a></li>
				<li><a href="index_transparent.html">Transparent Design</a></li>
				<li><a href="index_facebook.html">Facebook Design</a></li>
				<li><a href="index_google.html">Google Design</a></li>
			</ul>
		</div>
		<div class="jumbotron-cover"></div>
	</header>
	<div class="container">
		<div class="row">
			<div class="span12">
				<div class="well with-cover">
					<div class="well-cover" style="background-image: url(assets/images/default.jpg)"></div>
					<p>
						<strong>
							Last Updated: 13/February/2022<br>
							By: Sean Ngu<br>
							Email: <a href="mailto:<EMAIL>"><EMAIL></a>
						</strong>
					</p>
					<p>
						Thank you for purchasing my theme. If you have any questions that are beyond the scope of this help file,
						please feel free to email your question to my email <a href="mailTo:<EMAIL>"><EMAIL></a>. Thanks so much!
					</p>
			
				</div>
			</div><!-- END span12 -->
		</div><!-- END row -->
		<div class="row">
			<div class="span3 bs-docs-sidebar">
				<ul class="nav nav-list bs-docs-sidenav affix-top">
					<li class="active"><a href="#htmlStructure"><i class="icon-chevron-right"></i>HTML Structure</a></li>
					<li><a href="#page-head"><i class="icon-chevron-right"></i>Page Head</a></li>
					<li><a href="#page-top-menu"><i class="icon-chevron-right"></i>Page Header</a></li>
					<li><a href="#page-sidebar"><i class="icon-chevron-right"></i>Page Sidebar</a></li>
					<li><a href="#page-content"><i class="icon-chevron-right"></i>Page Content</a></li>
					<li><a href="#panel"><i class="icon-chevron-right"></i>Panel Box</a></li>
					<li><a href="#page-end"><i class="icon-chevron-right"></i>End of Page (Javascripts)</a></li>
					<li><a href="#page-theme-options"><i class="icon-chevron-right"></i>Page Theme Options</a></li>
					<li><a href="#page-dark-mode"><i class="icon-chevron-right"></i>Dark Mode <span class="label">NEW</span></a></li>
					<li><a href="#page-options"><i class="icon-chevron-right"></i>Page Options</a></li>
					<li><a href="#page-gulp-scss"><i class="icon-chevron-right"></i>Gulp & SCSS</a></li>
					<li><a href="#plugins-resources"><i class="icon-chevron-right"></i>Plugins & Resources</a></li>
					<li><a href="#references"><i class="icon-chevron-right"></i>References</a></li>
				</ul>
			</div>
			<div class="span9">
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="htmlStructure"><strong>A) HTML Structure</strong> - <a href="#top">top</a></h3>
						</div>
						<p>This template can be used as an fluid layout with a max of 12 columns next to each other. The general template structure is the same throughout the template. Here is the general structure.</p>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #loader --&gt;
&lt;div id="loader" class="app-loader"&gt;
  &lt;span class="spinner"&gt;&lt;/span&gt;
&lt;/div&gt;
&lt;!-- END #loader --&gt;

&lt;!-- BEGIN #app --&gt;
&lt;div id="app" class="app app-header-fixed app-sidebar-fixed "&gt;
  &lt;!-- BEGIN #header --&gt;
  &lt;div id="header" class="app-header"&gt;&lt;/div&gt;
  &lt;!-- BEGIN #header --&gt;
  
  &lt;!-- BEGIN #sidebar --&gt;
  &lt;div id="sidebar" class="app-sidebar"&gt;
    &lt;!-- BEGIN scrollbar --&gt;
    &lt;div class="app-sidebar-content" data-scrollbar="true" data-height="100%"&gt;
      ...
    &lt;/div&gt;
    &lt;!-- END scrollbar --&gt;
  &lt;/div&gt;
  &lt;div class="app-sidebar-bg"&gt;&lt;/div&gt;
  &lt;div class="app-sidebar-mobile-backdrop"&gt;
    &lt;a href="#" data-dismiss="app-sidebar-mobile" class="stretched-link"&gt;&lt;/a&gt;
  &lt;/div&gt;
  &lt;!-- END #sidebar --&gt;
  
  &lt;!-- BEGIN #content --&gt;
  &lt;div id="content" class="app-content"&gt;&lt;/div&gt;
  &lt;!-- END #content --&gt;
  
  &lt;!-- BEGIN theme-panel --&gt;
  &lt;div class="theme-panel"&gt;&lt;/div&gt;
  &lt;!-- END theme-panel --&gt;
    
  &lt;!-- BEGIN scroll-top-btn --&gt;
  &lt;a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" 
    data-toggle="scroll-to-top"&gt;
    &lt;i class="fa fa-angle-up"&gt;&lt;/i&gt;
  &lt;/a&gt;
  &lt;!-- END scroll-top-btn --&gt;
&lt;/div&gt;
&lt;!-- END #app --&gt;
</pre>
					</div><!-- END span12 -->
				</div><!-- END row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-head"><strong>B) Page Head</strong> - <a href="#top">top</a></h3>
						</div>
						<p>Page head contains metadata, javascript and css files:</p>
<pre class="prettyprint linenums">
&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
  &lt;meta charset="utf-8" /&gt;
  &lt;title&gt;Color Admin | Default Design&lt;/title&gt;
  &lt;meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" /&gt;
  &lt;meta content="" name="description" /&gt;
  &lt;meta content="" name="author" /&gt;
  
  &lt;!-- ================== BEGIN core-css ================== --&gt;
  &lt;link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" /&gt;
  &lt;link href="../assets/css/vendor.min.css" rel="stylesheet" /&gt;
  &lt;link href="../assets/css/default/app.min.css" rel="stylesheet" /&gt;
  &lt;!-- ================== END core-css ================== --&gt;
  
  &lt;!-- OR without vendor.min.css --&gt;
  
  &lt;!-- ================== BEGIN core-css ================== --&gt;
  &lt;link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" /&gt;
  &lt;link href="../assets/plugins/animate.css/animate.min.css" rel="stylesheet" /&gt;
  &lt;link href="../assets/plugins/@fortawesome/fontawesome-free/css/all.min.css" rel="stylesheet" /&gt;
  &lt;link href="../assets/plugins/jquery-ui-dist/jquery-ui.min.css" rel="stylesheet" /&gt;
  &lt;link href="../assets/plugins/pace-js/themes/black/pace-theme-flash.css" rel="stylesheet" /&gt;
  &lt;link href="../assets/plugins/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" /&gt;
  &lt;link href="../assets/css/default/app.min.css" rel="stylesheet" /&gt;
  &lt;!-- ================== END core-css ================== --&gt;
&lt;/head&gt;
&lt;body&gt;
</pre>
					</div><!-- END span12 -->
				</div><!-- END row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-top-menu"><strong>C) Header</strong> - <a href="#top">top</a></h3>
						</div>
						<p>Top menu structure:</p>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #header --&gt;
&lt;div id="header" class="app-header"&gt;
  &lt;!-- BEGIN navbar-header --&gt;
  &lt;div class="navbar-header"&gt;
    &lt;a href="index.html" class="navbar-brand"&gt;&lt;span class="navbar-logo"&gt;&lt;/span&gt; &lt;b&gt;Color&lt;/b&gt; Admin&lt;/a&gt;
    &lt;button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-mobile"&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
    &lt;/button&gt;
  &lt;/div&gt;
  &lt;!-- END navbar-header --&gt;
  &lt;!-- BEGIN header-nav --&gt;
  &lt;div class="navbar-nav"&gt;
    &lt;div class="navbar-item navbar-form"&gt;
      &lt;form action="" method="POST" name="search"&gt;
        &lt;div class="form-group"&gt;
          &lt;input type="text" class="form-control" placeholder="Enter keyword" /&gt;
          &lt;button type="submit" class="btn btn-search"&gt;&lt;i class="fa fa-search"&gt;&lt;/i&gt;&lt;/button&gt;
        &lt;/div&gt;
      &lt;/form&gt;
    &lt;/div&gt;
    ...
    &lt;div class="navbar-item navbar-user dropdown"&gt;
      &lt;a href="#" class="navbar-link dropdown-toggle d-flex align-items-center" 
        data-bs-toggle="dropdown"&gt;
        &lt;img src="../assets/img/user/user-13.jpg" alt="" /&gt; 
        &lt;span&gt;
          &lt;span class="d-none d-md-inline"&gt;Adam Schwartz&lt;/span&gt;
          &lt;b class="caret"&gt;&lt;/b&gt;
        &lt;/span&gt;
      &lt;/a&gt;
      &lt;div class="dropdown-menu dropdown-menu-end me-1"&gt;
        ...
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
  &lt;!-- END header-nav --&gt;
&lt;/div&gt;
&lt;!-- END #header --&gt;
</pre>
					</div><!-- END span12 -->
				</div><!-- END row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-sidebar"><strong>D) Sidebar</strong> - <a href="#top">top</a></h3>
						</div>
						<p>Sidebar structure:</p>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #sidebar --&gt; 
&lt;div id="sidebar" class="app-sidebar"&gt; 
  &lt;!-- BEGIN scrollbar --&gt; 
  &lt;div class="app-sidebar-content" data-scrollbar="true" data-height="100%"&gt; 
    &lt;!-- BEGIN menu --&gt; 
    &lt;div class="menu"&gt; 
      &lt;div class="menu-header"&gt; Navigation&lt;/div&gt; 
      &lt;div class="menu-item has-sub active"&gt; 
        &lt;a href="javascript:;" class="menu-link"&gt; 
          &lt;div class="menu-icon"&gt;&lt;i class="fa fa-th-large"&gt; &lt;/i&gt;&lt;/div&gt; 
          &lt;div class="menu-text"&gt; Dashboard&lt;/div&gt; 
          &lt;div class="menu-caret"&gt; &lt;/div&gt; 
        &lt;/a&gt; 
        &lt;div class="menu-submenu"&gt; 
          &lt;div class="menu-item active"&gt; 
            &lt;a href="index.html" class="menu-link"&gt;
              &lt;div class="menu-text"&gt; Dashboard v1&lt;/div&gt;
            &lt;/a&gt; 
          &lt;/div&gt;
          ...
        &lt;/div&gt; 
      &lt;/div&gt; 
      ...
      &lt;!-- BEGIN minify-button --&gt; 
      &lt;div class="menu-item d-flex"&gt; 
        &lt;a href="javascript:;" class="app-sidebar-minify-btn ms-auto" data-toggle="app-sidebar-minify"&gt;
          &lt;i class="fa fa-angle-double-left"&gt;&lt;/i&gt;
        &lt;/a&gt; 
      &lt;/div&gt; 
      &lt;!-- END minify-button --&gt; 
    &lt;/div&gt; 
    &lt;!-- END menu --&gt; 
  &lt;/div&gt; 
  &lt;!-- END scrollbar --&gt; 
&lt;/div&gt; 
&lt;div class="app-sidebar-bg"&gt; &lt;/div&gt; 
&lt;div class="app-sidebar-mobile-backdrop"&gt;
  &lt;a href="#" data-dismiss="app-sidebar-mobile" class="stretched-link"&gt; &lt;/a&gt;
&lt;/div&gt; 
&lt;!-- END #sidebar --&gt; 
</pre>
					</div><!-- END span12 -->
				</div><!-- END row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-content"><strong>E) Page Content</strong> - <a href="#top">top</a></h3>
						</div>
						<p>
							Page content consists of page title, breadcrumbs and page's main body. HTML code of Content container as shown below:
						</p>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #content --&gt;
&lt;div id="content" class="app-content"&gt;
  &lt;!-- BEGIN breadcrumb --&gt;
  &lt;ol class="breadcrumb float-xl-end"&gt;
    &lt;li class="breadcrumb-item"&gt;&lt;a href="javascript:;"&gt;Home&lt;/a&gt;&lt;/li&gt;
    &lt;li class="breadcrumb-item"&gt;&lt;a href="javascript:;"&gt;Page Options&lt;/a&gt;&lt;/li&gt;
    &lt;li class="breadcrumb-item active"&gt;Blank Page&lt;/li&gt;
  &lt;/ol&gt;
  &lt;!-- END breadcrumb --&gt;
  &lt;!-- BEGIN page-header --&gt;
  &lt;h1 class="page-header"&gt;Blank Page &lt;small&gt;header small text goes here...&lt;/small&gt;&lt;/h1&gt;
  &lt;!-- END page-header --&gt;
  
  &lt;!-- Page Content Here --&gt;  
&lt;/div&gt;
&lt;!-- END #content --&gt;
</pre>
					</div><!-- END span12 -->
				</div><!-- END row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="panel"><strong>F) Panel Box</strong> - <a href="#top">top</a></h3>
						</div>
						<p>
							Widget Box used for separate the content clearly by section.
						</p>
<pre class="prettyprint linenums">
&lt;!-- BEGIN panel --&gt;
&lt;div class="panel panel-inverse"&gt;
  &lt;div class="panel-heading"&gt;
    &lt;h4 class="panel-title"&gt;Panel Title here&lt;/h4&gt;
    &lt;div class="panel-heading-btn"&gt;
      &lt;a href="#" class="btn btn-xs btn-icon btn-circle btn-default" data-toggle="panel-expand"&gt;
        &lt;i class="fa fa-expand"&gt;&lt;/i&gt;
      &lt;/a&gt;
      &lt;a href="#" class="btn btn-xs btn-icon btn-circle btn-success" data-toggle="panel-reload"&gt;
        &lt;i class="fa fa-repeat"&gt;&lt;/i&gt;
      &lt;/a&gt;
      &lt;a href="#" class="btn btn-xs btn-icon btn-circle btn-warning" data-toggle="panel-collapse"&gt;
        &lt;i class="fa fa-minus"&gt;&lt;/i&gt;
      &lt;/a&gt;
      &lt;a href="#" class="btn btn-xs btn-icon btn-circle btn-danger" data-toggle="panel-remove"&gt;
        &lt;i class="fa fa-times"&gt;&lt;/i&gt;
      &lt;/a&gt;
    &lt;/div&gt;
  &lt;/div&gt;
  &lt;div class="panel-body"&gt;
    Panel Content Here
  &lt;/div&gt;
&lt;/div&gt;
&lt;!-- END panel --&gt;
</pre>
					</div><!-- END span12 -->
				</div><!-- END row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-end"><strong>G) End of Page (Javascript)</strong> - <a href="#top">top</a></h3>
						</div>
						<p>
							Javascript files loaded in the end of page. This will reduce page load time.
						</p>
<pre class="prettyprint linenums">
  &lt;!-- ================== BEGIN core-js ================== --&gt;
  &lt;script src="../assets/js/vendor.min.js"&gt;&lt;/script&gt;
  &lt;script src="../assets/js/app.min.js"&gt;&lt;/script&gt;
  &lt;!-- ================== END core-js ================== --&gt;
  
  &lt;!-- OR without vendor.min.js --&gt;
  
  &lt;!-- ================== BEGIN core-js ================== --&gt;
  &lt;script src="../assets/plugins/pace-js/pace.min.js"&gt;&lt;/script&gt;
  &lt;script src="../assets/plugins/jquery/dist/jquery.min.js"&gt;&lt;/script&gt;
  &lt;script src="../assets/plugins/jquery-ui-dist/jquery-ui.min.js"&gt;&lt;/script&gt;
  &lt;script src="../assets/plugins/bootstrap/dist/js/bootstrap.bundle.min.js"&gt;&lt;/script&gt;
  &lt;script src="../assets/plugins/perfect-scrollbar/dist/perfect-scrollbar.min.js"&gt;&lt;/script&gt;
  &lt;script src="../assets/plugins/js-cookie/dist/js.cookie.js"&gt;&lt;/script&gt;
  &lt;script src="../assets/js/app.min.js"&gt;&lt;/script&gt;
  &lt;!-- ================== END core-js ================== --&gt;
&lt;/body&gt;
&lt;/html&gt;
</pre>
					</div>
				</div><!-- END row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-theme-options"><strong>H) Page Theme Options</strong> - <a href="#top">top</a></h3>
						</div>
						<p>Add the theme to the <code>&lt;body&gt;</code> tag in order to change the theme color.</p>
						<h4>Available Theme Options</h4>
<pre class="prettyprint linenums">
&lt;body class="theme-red"&gt;...&lt;/body&gt;
&lt;body class="theme-pink"&gt;...&lt;/body&gt;
&lt;body class="theme-orange"&gt;...&lt;/body&gt;
&lt;body class="theme-yellow"&gt;...&lt;/body&gt;
&lt;body class="theme-lime"&gt;...&lt;/body&gt;
&lt;body class="theme-green"&gt;...&lt;/body&gt;
&lt;body class="theme-teal"&gt;...&lt;/body&gt;
&lt;body class="theme-cyan"&gt;...&lt;/body&gt;
&lt;body class="theme-blue"&gt;...&lt;/body&gt;
&lt;body class="theme-purple"&gt;...&lt;/body&gt;
&lt;body class="theme-indigo"&gt;...&lt;/body&gt;
&lt;body class="theme-black"&gt;...&lt;/body&gt;
</pre>
					</div><!-- END span12 -->
				</div><!-- END row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-dark-mode"><strong>I) Dark Mode</strong> - <a href="#top">top</a></h3>
						</div>
						<p>Add the <code>.dark-mode</code> class to <code>&lt;html&gt;</code> in order to enable the dark mode.</p>
<pre class="prettyprint linenums">
&lt;html class="dark-mode"&gt;
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-options"><strong>J) Page Options</strong> - <a href="#top">top</a></h3>
						</div>
						<p>Here is the general structure for the each case of page options.</p>
						<h4>Page Without Sidebar</h4>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #app --&gt;
&lt;div id="app" class="app app-without-sidebar"&gt;
  &lt;!-- BEGIN #sidebar --&gt;
  &lt;div id="sidebar" class="app-sidebar"&gt;
  &lt;!-- END #sidebar --&gt;
</pre>
						<h4>Page With Two Sidebar</h4>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #app --&gt;
&lt;div id="app" class="app  app-sidebar-two"&gt;
  &lt;!-- BEGIN #sidebar --&gt;
  &lt;div id="sidebar" class="app-sidebar"&gt;&lt;/div&gt;
  &lt;div class="app-sidebar-bg"&gt;&lt;/div&gt;
  &lt;div class="app-sidebar-mobile-backdrop"&gt;
    &lt;a href="#" data-dismiss="app-sidebar-mobile" class="stretched-link"&gt; &lt;/a&gt;
  &lt;/div&gt;
  &lt;!-- END #sidebar --&gt;
  
  &lt;!-- BEGIN #sidebar-right --&gt;
  &lt;div id="sidebar-right" class="app-sidebar app-sidebar-end"&gt;&lt;/div&gt;
  &lt;div class="app-sidebar-bg sidebar-end"&gt;&lt;/div&gt;
  &lt;div class="app-sidebar-mobile-backdrop app-sidebar-end"&gt;
    &lt;a href="#" data-dismiss="app-sidebar-end-mobile" class="stretched-link"&gt; &lt;/a&gt;
  &lt;/div&gt;
  &lt;!-- END #sidebar-right --&gt;
</pre>
						<h4>Page Without Header</h4>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #app --&gt;
&lt;div id="app" class="app app-without-header"&gt;
  ...
</pre>
						<h4>Top Navigation Default</h4>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #app --&gt;
&lt;div id="app" class="app"&gt;
  &lt;!-- BEGIN #header --&gt;
  &lt;div id="header" class="app-header"&gt;
</pre>
						<h4>Top Navigation Fixed</h4>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #app --&gt;
&lt;div id="app" class="app app-header-fixed"&gt;
  &lt;!-- BEGIN #header --&gt;
  &lt;div id="header" class="app-header"&gt;
</pre>
						<h4>Top Navigation Inverse Styling</h4>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #app --&gt;
&lt;div id="app" class="app"&gt;
  &lt;!-- BEGIN #header --&gt;
  &lt;div id="header" class="app-header app-header-inverse"&gt;
</pre>
						<h4>Sidebar Default</h4>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #app --&gt;
&lt;div id="app" class="app"&gt;
  &lt;!-- BEGIN #sidebar --&gt;
  &lt;div id="sidebar" class="app-sidebar"&gt;
    ...
  &lt;/div&gt;
  &lt;div class="sidebar-bg"&gt;&lt;/div&gt;
  &lt;!-- END #sidebar --&gt;
</pre>
						<h4>Sidebar Fixed</h4>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #app --&gt;
&lt;div id="app" class="app aopp-sidebar-fixed"&gt;
  &lt;!-- BEGIN #sidebar --&gt;
  &lt;div id="sidebar" class="app-sidebar"&gt;
</pre>
						<h4>Sidebar Minified</h4>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #app --&gt;
&lt;div id="app" class="app app-sidebar-minified"&gt;
  &lt;!-- BEGIN #sidebar --&gt;
  &lt;div id="sidebar" class="app-sidebar"&gt;
</pre>
						<h4>Sidebar Position Right</h4>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #app --&gt;
&lt;div id="app" class="app app-with-end-sidebar"&gt;
  &lt;!-- BEGIN #sidebar --&gt;
  &lt;div id="sidebar" class="app-sidebar"&gt;
</pre>
						<h4>Sidebar Grid Styling</h4>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #sidebar --&gt;
&lt;div id="sidebar" class="app-sidebar app-sidebar-grid"&gt;
</pre>
						<h4>Sidebar Gradient Styling <span class="label">NEW</span></h4>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #app --&gt;
&lt;div id="app" class="app app-gradient-enabled"&gt;
</pre>
						<h4>Full Height Page / Table Column Page <span class="label label-primary">NEW</span></h4>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #app --&gt;
&lt;div id="app" class="app app-content-full-height"&gt;
  &lt;!-- BEGIN #content --&gt;
  &lt;div id="content" class="app-content p-0"&gt;
    &lt;div class="overflow-hidden h-100"&gt;
      &lt;div data-scrollbar="true" data-height="100%" data-skip-mobile="true" 
        class="app-content-padding"&gt;
        ...
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
  &lt;!-- END #content --&gt;
&lt;/div&gt;
&lt;!-- END #app --&gt;
</pre>
						<h4 style="margin: 0 0 10px;">Remove Page Loader</h4>
						<p>If you wish to remove the page loading animation, you just need to remove the <code>#loader</code> from <code>#app</code></p>
						<p><b>from</b></p>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #loader --&gt;
&lt;div id="loader" class="app-loader"&gt;
  &lt;span class="spinner"&gt;&lt;/span&gt;
&lt;/div&gt;
&lt;!-- END #loader --&gt;
</pre>
					</div><!-- END span12 -->
				</div><!-- END row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-gulp-scss"><strong>K) Using Gulp SCSS</strong> - <a href="#top">top</a></h3>
						</div>
						<h4>Installation</h4>
						<p>Below is the command that required to run / compile the scss with gulpjs. If you are new to the gulpjs, you may refer to their <a href="https://gulpjs.com/" target="_blank">official website</a> for installation guide.</p>
<pre class="prettyprint linenums">
&lt;!-- run the following command --&gt;
cd /your-path-url/admin/src/
npm install
gulp

&lt;!-- available command --&gt;
gulp plugins       // generate plugins

&lt;!-- available theme command --&gt;
gulp               // default theme
gulp material      // material theme
gulp apple         // apple theme
gulp transparent   // transparent theme
gulp facebook      // facebook theme
gulp google        // google theme
</pre>
						<p>Verify that you are running at least node 10.9.x or later and npm 6.x.x by running node -v and npm -v in a terminal/console window. Older versions produce errors, but newer versions are fine.</p>
						<hr />
						<h4>Enable RTL Support</h4>
						<p>
							To enable the RTL Support, you might need to change the following variable from <code>admin/src/scss/default/_variables.scss</code>.
						</p>
<pre class="prettyprint linenums">
&lt;!-- LINE 222 --&gt;
$enable-rtl: true;

&lt;!-- run the command to regenerate the /css/default/app.min.css --&gt;
gulp
</pre>
						<hr />
						<h4>Gulp compilation</h4>
						<p>
							We are using gulp to compile & combine few plugins js & css into one single file for js & css. This may help to improve the speed for page load if compare to multiple files loading in one page. 
							If you wish to edit the included plugins files, you may refer to the <code>/admin/src/gulpfile.js</code>.
						</p>
						<p>Below is the list of plugins that compile into <code>vendor.min.css</code>.</p>
<pre class="prettyprint linenums">
return gulp.src([
  'node_modules/animate.css/animate.min.css',
  'node_modules/@fortawesome/fontawesome-free/css/all.min.css',
  'node_modules/jquery-ui-dist/jquery-ui.min.css',
  'node_modules/pace-js/themes/black/pace-theme-flash.css',
  'node_modules/perfect-scrollbar/css/perfect-scrollbar.css'
])
.pipe(sass())
.pipe(concat('vendor.min.css'))
.pipe(minifyCSS())
.pipe(gulp.dest(distPath + '/assets/css/'))
.pipe(livereload());
</pre>
						<p>Below is the code that compile Color Admin scss with Bootstrap scss into <code>app.min.css</code>.</p>
<pre class="prettyprint linenums">
return gulp.src([
  'scss/default/styles.scss'
])
.pipe(sass())
.pipe(concat('app.min.css'))
.pipe(minifyCSS())
.pipe(gulp.dest(distPath + '/assets/css/default/'))
.pipe(livereload());
</pre>
						<p>Below is the list of plugins that compile into <code>vendor.min.js</code>.</p>
<pre class="prettyprint linenums">
'node_modules/pace-js/pace.min.js',
  'node_modules/jquery/dist/jquery.min.js',
  'node_modules/jquery-ui-dist/jquery-ui.min.js',
  'node_modules/bootstrap/dist/js/bootstrap.bundle.min.js',
  'node_modules/perfect-scrollbar/dist/perfect-scrollbar.min.js',
  'node_modules/js-cookie/dist/js.cookie.js'
])
.pipe(sourcemaps.init())
.pipe(concat('vendor.min.js'))
.pipe(sourcemaps.write())
.pipe(uglify())
.pipe(gulp.dest(distPath + '/assets/js/'))
.pipe(livereload());
</pre>
						<p>Below is the code that compile Color Admin js file into <code>app.min.js</code>.</p>
<pre class="prettyprint linenums">
return gulp.src([
  'js/app.js',
])
.pipe(sourcemaps.init())
.pipe(concat('app.min.js'))
.pipe(sourcemaps.write())
.pipe(uglify())
.pipe(gulp.dest(distPath + '/assets/js/'))
.pipe(livereload());
</pre>
						<hr />
						<h4>Gulp & SCSS file structure</h4>
						<p>To change the color theme / primary color, you may refer to the <code>_variables.scss</code> and set the variable. Npm can be used to update the plugins version as well.</p>

<pre class="prettyprint linenums">
admin/src/
├── gulpfile.js
├── package.json
├── js/
└── scss/
    ├── default/
    │   ├── _app.scss
    │   ├── _functions.scss
    │   ├── _helper.scss
    │   ├── _layout.scss
    │   ├── _mixins.scss
    │   ├── _pages.scss
    │   ├── _plugins.scss
    │   ├── _root.scss
    │   ├── _reboot.scss
    │   ├── _rtl.scss
    │   ├── _ui.scss
    │   ├── _variables.scss
    │   ├── styles.scss
    │   ├── app/
    │   ├── images/
    │   ├── layout/
    │   ├── mixins/
    │   ├── pages/
    │   ├── plugins/
    │   └── ui/
    ├── apple/
    ├── facebook/
    ├── material/
    ├── transparent/
    └── google/
</pre>
					</div><!-- END span12 -->
				</div><!-- END row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="plugins-resources"><strong>L) Plugins & Resources</strong> - <a href="#top">top</a></h3>
						</div>
						<p>
							Below is the list of all plugins and external resources used to power this template.
						</p>
						<table class="table table-bordered">
							<thead>
								<tr>
									<th style="width:100px;">Name</th>
									<th>CSS Files</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>Bootstrap 5</td>
									<td>
										<p>Bootstrap 5 has been included in <code>app.min.css</code> & <code>vendor.min.js</code></p>
										<p><b>Official Link:</b><br /> <a href="http://getbootstrap.com/">http://getbootstrap.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>FontAwesome</td>
									<td>
										<p>FontAwesome has been included in <code>vendor.min.css</code></p>
										<p><b>Official Link:</b><br /> <a href="https://fontawesome.com/">https://fontawesome.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>Animate.css</td>
									<td>
										<p>Animate.css has been included in <code>vendor.min.css</code></p>
										<p><b>Official Link:</b><br /> <a href="http://daneden.github.io/animate.css/">http://daneden.github.io/animate.css/</a></p>
									</td>
								</tr>
								<tr>
									<td>jQuery</td>
									<td>
										<p>jQuery has been included in <code>vendor.min.js</code></p>
										<p><b>Official Link:</b><br /> <a href="http://jquery.com/">http://jquery.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>jQuery UI</td>
									<td>
										<p>jQuery UI has been included in <code>vendor.min.css</code> & <code>vendor.min.js</code></p>
										<p><b>Official Link:</b><br /> <a href="http://jqueryui.com/">http://jqueryui.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>Perfect Scrollbar</td>
									<td>
										<p>Perfect Scrollbar has been included in <code>vendor.min.css</code> & <code>vendor.min.js</code></p>
										<p><b>Official Link:</b><br /> <a href="https://mdbootstrap.com/freebies/perfect-scrollbar/">https://mdbootstrap.com/freebies/perfect-scrollbar/</a></p>
									</td>
								</tr>
								<tr>
									<td>JS Cookie</td>
									<td>
										<p>JS Cookie has been included in <code>app.min.js</code></p>
										<p><b>Official Link:</b><br /> <a href="https://github.com/js-cookie/js-cookie">https://github.com/js-cookie/js-cookie</a></p>
									</td>
								</tr>
								<tr>
									<td>Bootstrap Icons</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://icons.getbootstrap.com/">https://icons.getbootstrap.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>jvectormap</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/jvectormap-next/jquery-jvectormap.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/jvectormap-next/jquery-jvectormap.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/jvectormap-next/jquery-jvectormap-world-mill.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://jvectormap.com/">http://jvectormap.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>flot charts</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/flot/source/jquery.canvaswrapper.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/flot/source/jquery.colorhelpers.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/flot/source/jquery.flot.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/flot/source/jquery.flot.saturated.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/flot/source/jquery.flot.browser.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/flot/source/jquery.flot.drawSeries.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/flot/source/jquery.flot.uiConstants.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://www.flotcharts.org/">http://www.flotcharts.org/</a></p>
									</td>
								</tr>
								<tr>
									<td>fullcalendar</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/@fullcalendar/common/main.min.css" rel="stylesheet" /&gt;
&lt;link href="../assets/plugins/@fullcalendar/daygrid/main.min.css" rel="stylesheet" /&gt;
&lt;link href="../assets/plugins/@fullcalendar/timegrid/main.min.css" rel="stylesheet" /&gt;
&lt;link href="../assets/plugins/@fullcalendar/list/main.min.css" rel="stylesheet" /&gt;
&lt;link href="../assets/plugins/@fullcalendar/bootstrap/main.min.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/moment/min/moment.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/@fullcalendar/core/main.global.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/@fullcalendar/daygrid/main.global.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/@fullcalendar/timegrid/main.global.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/@fullcalendar/interaction/main.global.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/@fullcalendar/list/main.global.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/@fullcalendar/bootstrap/main.global.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://fullcalendar.io/">https://fullcalendar.io/</a></p>
									</td>
								</tr>
								<tr>
									<td>Gritter</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/gritter/css/jquery.gritter.css" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/gritter/js/jquery.gritter.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://github.com/jboesch/Gritter">https://github.com/jboesch/Gritter</a></p>
									</td>
								</tr>
								<tr>
									<td>Masonry</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/masonry-layout/dist/masonry.pkgd.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://masonry.desandro.com/">https://masonry.desandro.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>Lightbox 2</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/lightbox2/dist/css/lightbox.css" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/lightbox2/dist/js/lightbox.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://lokeshdhakar.com/projects/lightbox2/">http://lokeshdhakar.com/projects/lightbox2/</a></p>
									</td>
								</tr>
								<tr>
									<td>Bootstrap Datepicker</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://uxsolutions.github.io/bootstrap-datepicker/">https://uxsolutions.github.io/bootstrap-datepicker/</a></p>
									</td>
								</tr>
								<tr>
									<td>Bootstrap Timepicker</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://github.com/jdewit/bootstrap-timepicker">https://github.com/jdewit/bootstrap-timepicker</a></p>
									</td>
								</tr>
								<tr>
									<td>Spectrum</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/spectrum-colorpicker2/dist/spectrum.min.css" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/spectrum-colorpicker2/dist/spectrum.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://seballot.github.io/spectrum/">https://seballot.github.io/spectrum/</a></p>
									</td>
								</tr>
								<tr>
									<td>Tag It</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/tag-it/css/jquery.tagit.css" />
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/jquery-migrate/dist/jquery-migrate.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/tag-it/js/tag-it.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://aehlke.github.io/tag-it/">http://aehlke.github.io/tag-it/</a></p>
									</td>
								</tr>
								<tr>
									<td>Masked Input</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/jquery.maskedinput/src/jquery.maskedinput.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://github.com/excellalabs/jquery.maskedinput">https://github.com/excellalabs/jquery.maskedinput</a></p>
									</td>
								</tr>
								<tr>
									<td>Parsley Validation</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/parsleyjs/dist/parsley.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://parsleyjs.org/">http://parsleyjs.org/</a></p>
									</td>
								</tr>
								<tr>
									<td>Bootstrap Wysihtml5</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/bootstrap3-wysihtml5-bower/dist/bootstrap3-wysihtml5.min.css" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/bootstrap3-wysihtml5-bower/dist/bootstrap3-wysihtml5.all.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://github.com/Waxolunist/bootstrap3-wysihtml5-bower">https://github.com/Waxolunist/bootstrap3-wysihtml5-bower</a></p>
									</td>
								</tr>
								<tr>
									<td>CKEditor</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/ckeditor/ckeditor.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://ckeditor.com/">http://ckeditor.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>Sparkline</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/jquery-sparkline/jquery.sparkline.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://omnipotent.net/jquery.sparkline/">http://omnipotent.net/jquery.sparkline/</a></p>
									</td>
								</tr>
								<tr>
									<td>DataTables</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/datatables.net/js/jquery.dataTables.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://www.datatables.net/">http://www.datatables.net/</a></p>
										
										<p><b>Extension:</b></p>
									    - Buttons
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/datatables.net-buttons-bs5/css/buttons.bootstrap5.min.css" /&gt;
&lt;script src="../assets/plugins/datatables.net-buttons/js/dataTables.buttons.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-buttons-bs5/js/buttons.bootstrap5.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-buttons/js/buttons.colVis.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-buttons/js/buttons.flash.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-buttons/js/buttons.html5.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-buttons/js/buttons.print.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/pdfmake/build/pdfmake.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/pdfmake/build/vfs_fonts.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/jszip/dist/jszip.min.js"&gt;&lt;/script&gt;
</pre>
									    - colReorder
</pre>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/datatables.net-colreorder-bs5/css/colreorder.bootstrap5.min.css" /&gt;
&lt;script src="../assets/plugins/datatables.net-colreorder/js/dataTables.colreorder.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-colreorder-bs5/js/colreorder.bootstrap5.min.js"&gt;&lt;/script&gt;
</pre>
									    - fixedColumns
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/datatables.net-fixedcolumns-bs5/css/fixedcolumns.bootstrap5.min.css" /&gt;
&lt;script src="../assets/plugins/datatables.net-fixedcolumns/js/dataTables.fixedcolumns.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-fixedcolumns-bs5/js/fixedcolumns.bootstrap5.min.js"&gt;&lt;/script&gt;
</pre>
									    - fixedHeader
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/datatables.net-fixedheader-bs5/css/fixedheader.bootstrap5.min.css" /&gt;
&lt;script src="../assets/plugins/datatables.net-fixedheader/js/dataTables.fixedheader.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-fixedheader-bs5/js/fixedheader.bootstrap5.min.js"&gt;&lt;/script&gt;
</pre>
									    - keyTable
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/datatables.net-keytable-bs5/css/keytable.bootstrap5.min.css" /&gt;
&lt;script src="../assets/plugins/datatables.net-keytable/js/dataTables.keytable.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-keytable-bs5/js/keytable.bootstrap5.min.js"&gt;&lt;/script&gt;
</pre>
									    - Responsive
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css" /&gt;
&lt;script src="../assets/plugins/datatables.net-responsive/js/dataTables.responsive.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js"&gt;&lt;/script&gt;
</pre>
									    - rowReorder
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/datatables.net-rowreorder-bs5/css/rowreorder.bootstrap5.min.css" /&gt;
&lt;script src="../assets/plugins/datatables.net-rowreorder/js/dataTables.rowreorder.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-rowreorder-bs5/js/rowreorder.bootstrap5.min.js"&gt;&lt;/script&gt;
</pre>
									    - scroller
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/datatables.net-scroller-bs5/css/scroller.bootstrap5.min.css" /&gt;
&lt;script src="../assets/plugins/datatables.net-scroller/js/dataTables.scroller.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-scroller-bs5/js/scroller.bootstrap5.min.js"&gt;&lt;/script&gt;
</pre>
									    - select
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/datatables.net-select-bs5/css/select.bootstrap5.min.css" /&gt;
&lt;script src="../assets/plugins/datatables.net-select/js/dataTables.select.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/datatables.net-select-bs5/js/select.bootstrap5.min.js"&gt;&lt;/script&gt;
</pre>
									</td>
								</tr>
								<tr>
									<td>Ion Range Slider</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/ion-rangeslider/css/ion.rangeSlider.min.css" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/ion-rangeslider/js/ion.rangeSlider.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://ionden.com/a/plugins/ion.rangeSlider/en.html">http://ionden.com/a/plugins/ion.rangeSlider/en.html</a></p>
									</td>
								</tr>
								<tr>
									<td>jQuery Countdown</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/countdown/jquery.countdown.css" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/countdown/jquery.plugin.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/countdown/jquery.countdown.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://countdownjs.org/">http://countdownjs.org/</a></p>
									</td>
								</tr>
								<tr>
									<td>Zurb Email Template</td>
									<td>
										<p><b>Official Link:</b><br /> <a href="https://foundation.zurb.com/emails/email-templates.html">https://foundation.zurb.com/emails/email-templates.html</a></p>
									</td>
								</tr>
								<tr>
									<td>Switchery</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/switchery/switchery.min.css" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/switchery/switchery.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://abpetkov.github.io/switchery/">http://abpetkov.github.io/switchery/</a></p>
									</td>
								</tr>
								<tr>
									<td>Powerange</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href=".../assets/plugins/abpetkov-powerange/dist/powerange.min.css" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/abpetkov-powerange/dist/powerange.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://abpetkov.github.io/powerange/">http://abpetkov.github.io/powerange/</a></p>
									</td>
								</tr>
								<tr>
									<td>X-Editable</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/x-editable-bs4/dist/bootstrap4-editable/css/bootstrap-editable.css" rel="stylesheet" /&gt;
&lt;link href="../assets/plugins/x-editable-bs4/dist/inputs-ext/address/address.css" rel="stylesheet" /&gt;
&lt;link href="../assets/plugins/x-editable-bs4/dist/inputs-ext/typeaheadjs/lib/typeahead.js-bootstrap.css" rel="stylesheet" /&gt;
&lt;link href="../assets/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" /&gt;
&lt;link href="../assets/plugins/bootstrap3-wysihtml5-bower/dist/bootstrap3-wysihtml5.min.css" rel="stylesheet" /&gt;
&lt;link href="../assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.min.css" rel="stylesheet" /&gt;
&lt;link href="../assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker3.min.css" rel="stylesheet" /&gt;
&lt;link href="../assets/plugins/select2/dist/css/select2.min.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/jquery-migrate/dist/jquery-migrate.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/x-editable-bs4/dist/bootstrap4-editable/js/bootstrap-editable.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/x-editable-bs4/dist/inputs-ext/address/address.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/x-editable-bs4/dist/inputs-ext/typeaheadjs/lib/typeahead.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/x-editable-bs4/dist/inputs-ext/typeaheadjs/typeaheadjs.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/x-editable-bs4/dist/inputs-ext/wysihtml5/wysihtml5.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/bootstrap3-wysihtml5-bower/dist/bootstrap3-wysihtml5.all.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/bootstrap-datetime-picker/js/bootstrap-datetimepicker.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/select2/dist/js/select2.full.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/jquery-mockjax/dist/jquery.mockjax.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/moment/min/moment.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://vitalets.github.io/x-editable/">http://vitalets.github.io/x-editable/</a></p>
									</td>
								</tr>
								<tr>
									<td>jQuery File Upload</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/blueimp-gallery/css/blueimp-gallery.min.css" rel="stylesheet" /&gt;
&lt;link href="../assets/plugins/blueimp-file-upload/css/jquery.fileupload.css" rel="stylesheet" /&gt;
&lt;link href="../assets/plugins/blueimp-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/blueimp-file-upload/js/vendor/jquery.ui.widget.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/blueimp-tmpl/js/tmpl.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/blueimp-load-image/js/load-image.all.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/blueimp-canvas-to-blob/js/canvas-to-blob.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/blueimp-gallery/js/jquery.blueimp-gallery.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/blueimp-file-upload/js/jquery.iframe-transport.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/blueimp-file-upload/js/jquery.fileupload.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/blueimp-file-upload/js/jquery.fileupload-process.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/blueimp-file-upload/js/jquery.fileupload-image.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/blueimp-file-upload/js/jquery.fileupload-audio.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/blueimp-file-upload/js/jquery.fileupload-video.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/blueimp-file-upload/js/jquery.fileupload-validate.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/blueimp-file-upload/js/jquery.fileupload-ui.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://blueimp.github.io/jQuery-File-Upload/">http://blueimp.github.io/jQuery-File-Upload/</a></p>
									</td>
								</tr>
								<tr>
									<td>jQuery-Knob</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/jquery-knob/dist/jquery.knob.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://github.com/aterrien/jQuery-Knob">https://github.com/aterrien/jQuery-Knob</a></p>
									</td>
								</tr>
								<tr>
									<td>Simple Line Icons</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link rel="stylesheet" href="../assets/plugins/simple-line-icons/css/simple-line-icons.css" /&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://thesabbir.github.io/simple-line-icons">https://thesabbir.github.io/simple-line-icons</a></p>
									</td>
								</tr>
								<tr>
									<td>Superbox</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/superbox/superbox.min.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/superbox/jquery.superbox.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://github.com/seyDoggy/superbox">https://github.com/seyDoggy/superbox</a></p>
									</td>
								</tr>
								<tr>
									<td>Chart JS</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/chart.js/dist/Chart.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://www.chartjs.org/">http://www.chartjs.org/</a></p>
									</td>
								</tr>
								<tr>
									<td>JS Tree</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/jstree/dist/themes/default/style.min.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/jstree/dist/jstree.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://www.jstree.com/">http://www.jstree.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>Bootstrap Date Range Picker</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/moment/min/moment.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/bootstrap-daterangepicker/daterangepicker.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://github.com/dangrossman/bootstrap-daterangepicker">https://github.com/dangrossman/bootstrap-daterangepicker</a></p>
									</td>
								</tr>
								<tr>
									<td>Select 2</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/select2/dist/css/select2.min.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/select2/dist/js/select2.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://select2.github.io/">https://select2.github.io/</a></p>
									</td>
								</tr>
								<tr>
									<td>Flag Icon</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/flag-icon-css/css/flag-icons.min.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://github.com/lipis/flag-icon-css">https://github.com/lipis/flag-icon-css</a></p>
									</td>
								</tr>
								<tr>
									<td>Bootstrap DateTime Picker</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://eonasdan.github.io/bootstrap-datetimepicker/">http://eonasdan.github.io/bootstrap-datetimepicker/</a></p>
									</td>
								</tr>
								<tr>
									<td>Nvd3</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/nvd3/build/nv.d3.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/d3/d3.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/nvd3/build/nv.d3.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://nvd3.org/">http://nvd3.org/</a></p>
									</td>
								</tr>
								<tr>
									<td>Boostrap Social</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/bootstrap-social/bootstrap-social.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://lipis.github.io/bootstrap-social/">https://lipis.github.io/bootstrap-social/</a></p>
									</td>
								</tr>
								<tr>
									<td>Intro JS</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/intro.js/minified/introjs.min.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/intro.js/minified/intro.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://introjs.com/">http://introjs.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>Dropzone</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/dropzone/dist/min/dropzone.min.css" rel="stylesheet" /&gt;
</pre>								
                                        <p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/dropzone/dist/min/dropzone.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://www.dropzonejs.com/">http://www.dropzonejs.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>Summernote</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/summernote/dist/summernote-lite.css" rel="stylesheet" /&gt;
</pre>	
                                        <p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/summernote/dist/summernote-lite.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://summernote.org/">http://summernote.org/</a></p>
									</td>
								</tr>
								<tr>
									<td>SweetAlert</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/sweetalert/dist/sweetalert.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://sweetalert.js.org/">https://sweetalert.js.org/</a></p>
									</td>
								</tr>
								<tr>
									<td>clipboard.js</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/clipboard/dist/clipboard.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://clipboardjs.com/">https://clipboardjs.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>Highlight JS</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/@highlightjs/cdn-assets/highlight.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://highlightjs.org/">https://highlightjs.org/</a></p>
									</td>
								</tr>
								<tr>
									<td>Apex Chart</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/apexcharts/dist/apexcharts.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://apexcharts.com/">https://apexcharts.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>Lity</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/lity/dist/lity.min.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/lity/dist/lity.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://sorgalla.com/lity/">http://sorgalla.com/lity/</a></p>
									</td>
								</tr>
								<tr>
									<td>Ionicons</td>
									<td>
										<p><b>Required CSS File</b></p>
<pre class="prettyprint linenums">
&lt;link href="../assets/plugins/ionicons/css/ionicons.min.css" rel="stylesheet" /&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://ionicons.com/">https://ionicons.com/</a></p>
									</td>
								</tr>
								<tr>
									<td>jQuery Mockjax</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/jquery-mockjax/dist/jquery.mockjax.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://github.com/jakerella/jquery-mockjax">https://github.com/jakerella/jquery-mockjax</a></p>
									</td>
								</tr>
								<tr>
									<td>jQuery Migrate</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/jquery-migrate/dist/jquery-migrate.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://github.com/jquery/jquery-migrate">https://github.com/jquery/jquery-migrate</a></p>
									</td>
								</tr>
								<tr>
									<td>AngularJS</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/angular/angular.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://angularjs.org/">http://angularjs.org/</a></p>
									</td>
								</tr>
								<tr>
									<td>Angular UI Router</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/angular-ui-router/release/angular-ui-router.min.js"&gt;&lt;/script&gt;
&lt;script src="../assets/plugins/angular-ui-router/release/stateEvents.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://ui-router.github.io/">https://ui-router.github.io/</a></p>
									</td>
								</tr>
								<tr>
									<td>Angular UI Bootstrap</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/angular-ui-bootstrap/dist/ui-bootstrap-tpls.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="http://angular-ui.github.io/bootstrap/">http://angular-ui.github.io/bootstrap/</a></p>
									</td>
								</tr>
								<tr>
									<td>Angular oclazyload</td>
									<td>
										<p><b>Required JS File</b></p>
<pre class="prettyprint linenums">
&lt;script src="../assets/plugins/ocLazyLoad/dist/ocLazyLoad.min.js"&gt;&lt;/script&gt;
</pre>
										<p><b>Official Link:</b><br /> <a href="https://oclazyload.readme.io">https://oclazyload.readme.io</a></p>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div><!-- END row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="references"><strong>M) References</strong> - <a href="#top">top</a></h3>
						</div>
						<p>
							I've used the following images, icons or other files as listed.
						</p>
						<p><b>jQuery Plugins</b></p>
						<ol>
							<li><b>Angular 13</b>:<a href="https://angular.io/">https://angular.io/</a></li>
							<li><b>Angular JS</b>:<a href="http://angularjs.org/">http://angularjs.org/</a></li>
							<li><b>Angular UI Router</b>:<a href="https://ui-router.github.io/">https://ui-router.github.io/</a></li>
							<li><b>Angular UI Bootstrap</b>:<a href="http://angular-ui.github.io/bootstrap/">http://angular-ui.github.io/bootstrap/</a></li>
							<li><b>Apex Chart</b>:<a href="https://apexcharts.com/">https://apexcharts.com/</a></li>
							<li><b>Animate.css</b>:<a href="http://daneden.github.io/animate.css/">http://daneden.github.io/animate.css/</a></li>
							<li><b>Bootstrap</b>:<a href="http://getbootstrap.com/">http://getbootstrap.com/</a></li>
							<li><b>Bootstrap Icons</b>:<a href="https://icons.getbootstrap.com/">https://icons.getbootstrap.com/</a></li>
							<li><b>Bootstrap Datepicker</b>:<a href="https://uxsolutions.github.io/bootstrap-datepicker/">https://uxsolutions.github.io/bootstrap-datepicker/</a></li>
							<li><b>Bootstrap Timepicker</b>:<a href="http://jdewit.github.io/bootstrap-timepicker/">http://jdewit.github.io/bootstrap-timepicker/</a></li>
							<li><b>Bootstrap Social</b>:<a href="https://lipis.github.io/bootstrap-social/">https://lipis.github.io/bootstrap-social/</a></li>
							<li><b>Bootstrap Datetimepicker</b>:<a href="http://eonasdan.github.io/bootstrap-datetimepicker/">http://eonasdan.github.io/bootstrap-datetimepicker/</a></li>
							<li><b>Bootstrap Daterangepicker</b>:<a href="https://github.com/dangrossman/bootstrap-daterangepicker">https://github.com/dangrossman/bootstrap-daterangepicker</a></li>
							<li><b>Bootstrap WYSIHTML5</b>:<a href="https://github.com/Waxolunist/bootstrap3-wysihtml5-bower">https://github.com/Waxolunist/bootstrap3-wysihtml5-bower</a></li>
							<li><b>Chart JS</b>:<a href="http://www.chartjs.org/">http://www.chartjs.org/</a></li>
							<li><b>CKEditor</b>:<a href="http://ckeditor.com/">http://ckeditor.com/</a></li>
							<li><b>Clipboard</b>:<a href="https://clipboardjs.com/">https://clipboardjs.com/</a></li>
							<li><b>DataTables</b>:<a href="https://www.datatables.net/">https://www.datatables.net/</a></li>
							<li><b>Dropzone</b>:<a href="http://www.dropzonejs.com/">http://www.dropzonejs.com/</a></li>
							<li><b>FontAwesome</b>:<a href="https://fontawesome.com/">https://fontawesome.com/</a></li>
							<li><b>Fullcalendar</b>:<a href="https://fullcalendar.io/">https://fullcalendar.io/</a></li>
							<li><b>Flot chart</b>:<a href="http://www.flotcharts.org/">http://www.flotcharts.org/</a></li>
							<li><b>Flag Icon</b>:<a href="https://github.com/lipis/flag-icon-css">https://github.com/lipis/flag-icon-css</a></li>
							<li><b>Gritter</b>:<a href="https://github.com/jboesch/Gritter">https://github.com/jboesch/Gritter</a></li>
							<li><b>Highlight.js</b>:<a href="https://highlightjs.org/">https://highlightjs.org/</a></li>
							<li><b>Ionicons</b>:<a href="https://ionicons.com/">https://ionicons.com/</a></li>
							<li><b>Ion Range Slider</b>:<a href="http://ionden.com/a/plugins/ion.rangeSlider/en.html">http://ionden.com/a/plugins/ion.rangeSlider/en.html</a></li>
							<li><b>Intro.js</b>:<a href="http://introjs.com/">http://introjs.com/</a></li>
							<li><b>jQuery</b>:<a href="http://jquery.com/">http://jquery.com/</a></li>
							<li><b>jQuery UI</b>:<a href="http://jqueryui.com/">http://jqueryui.com/</a></li>
							<li><b>jQuery slimScroll</b>:<a href="http://rocha.la/jQuery-slimScroll">http://rocha.la/jQuery-slimScroll</a></li>
							<li><b>jQuery File Upload</b>:<a href="http://blueimp.github.io/jQuery-File-Upload/">http://blueimp.github.io/jQuery-File-Upload/</a></li>
							<li><b>jQuery Knob</b>:<a href="https://github.com/aterrien/jQuery-Knob">https://github.com/aterrien/jQuery-Knob</a></li>
							<li><b>jVectormap</b>:<a href="http://jvectormap.com/">http://jvectormap.com/</a></li>
							<li><b>jQuery Mockjax</b>:<a href="https://github.com/jakerella/jquery-mockjax">https://github.com/jakerella/jquery-mockjax</a></li>
							<li><b>jQuery Migrate</b>:<a href="https://github.com/jquery/jquery-migrate">https://github.com/jquery/jquery-migrate</a></li>
							<li><b>jQuery Countdown</b>:<a href="http://countdownjs.org/">http://countdownjs.org/</a></li>
							<li><b>JS Cookie</b>:<a href="https://github.com/js-cookie/js-cookie">https://github.com/js-cookie/js-cookie</a></li>
							<li><b>JS Tree</b>:<a href="http://www.jstree.com/">http://www.jstree.com/</a></li>
							<li><b>Laravel</b>:<a href="https://laravel.com/">https://laravel.com/</a></li>
							<li><b>Lity</b>:<a href="http://sorgalla.com/lity/">http://sorgalla.com/lity/</a></li>
							<li><b>Lightbox 2</b>:<a href="http://lokeshdhakar.com/projects/lightbox2/">http://lokeshdhakar.com/projects/lightbox2/</a></li>
							<li><b>Masked Input</b>:<a href="https://github.com/excellalabs/jquery.maskedinput">https://github.com/excellalabs/jquery.maskedinput</a></li>
							<li><b>Masonry</b>:<a href="https://masonry.desandro.com/">https://masonry.desandro.com/</a></li>
							<li><b>Morris Chart</b>:<a href="http://morrisjs.github.io/morris.js/index.html">http://morrisjs.github.io/morris.js/index.html</a></li>
							<li><b>Nvd3</b>:<a href="http://nvd3.org/">http://nvd3.org/</a></li>
							<li><b>oclazyload</b>:<a href="https://oclazyload.readme.io">https://oclazyload.readme.io</a></li>
							<li><b>Parsley Validation</b>:<a href="http://parsleyjs.org/">http://parsleyjs.org/</a></li>
							<li><b>Powerange</b>:<a href="http://abpetkov.github.io/powerange/">http://abpetkov.github.io/powerange/</a></li>
							<li><b>React.js</b>:<a href="https://reactjs.org/">https://reactjs.org/</a></li>
							<li><b>Spectrum</b>:<a href="https://seballot.github.io/spectrum/">https://seballot.github.io/spectrum/</a></li>
							<li><b>Summernote</b>:<a href="http://summernote.org/">http://summernote.org/</a></li>
							<li><b>Superbox</b>:<a href="https://github.com/seyDoggy/superbox">https://github.com/seyDoggy/superbox</a></li>
							<li><b>SweetAlert</b>:<a href="https://sweetalert.js.org/">https://sweetalert.js.org/</a></li>
							<li><b>Select 2</b>:<a href="https://select2.github.io/">https://select2.github.io/</a></li>
							<li><b>Switchery</b>:<a href="http://abpetkov.github.io/switchery/">http://abpetkov.github.io/switchery/</a></li>
							<li><b>Sparkline</b>:<a href="http://omnipotent.net/jquery.sparkline/">http://omnipotent.net/jquery.sparkline/</a></li>
							<li><b>Simple Line Icons</b>:<a href="https://thesabbir.github.io/simple-line-icons">https://thesabbir.github.io/simple-line-icons</a></li>
							<li><b>Tag It</b>:<a href="http://aehlke.github.io/tag-it/">http://aehlke.github.io/tag-it/</a></li>
							<li><b>Vue.js</b>:<a href="https://vuejs.org/">https://vuejs.org/</a></li>
							<li><b>X-editable</b>:<a href="http://vitalets.github.io/x-editable/">http://vitalets.github.io/x-editable/</a></li>
							<li><b>Zurb Email Templates</b>:<a href="https://foundation.zurb.com/emails/email-templates.html">https://foundation.zurb.com/emails/email-templates.html</a></li>
						</ol>
					</div>
				</div><!-- END row-fluid -->
			</div><!-- END span12 -->
		</div><!-- END row-fluid -->
	</div><!-- END container -->
	
	<footer class="footer">
		<div class="container text-left">
			<p>Once again, thank you so much for purchasing this theme. As I said at the beginning, I'd be glad to help you if you have any questions relating to this theme. No guarantees, but I'll do my best to assist. If you have a more general question relating to the themes, you might consider visiting the forums and asking your question via <a href="mailTo:<EMAIL>">email</a>.</p> 
			<br />
			<p class="append-bottom alt large"><strong>Sean Ngu</strong></p>
			<p><a href="#top">Go To Table of Contents</a></p>
		</div>
	</footer><!-- END footer -->
	
	<script src="assets/bootstrap/js/jquery.js"></script>
	<script src="assets/bootstrap/js/bootstrap-transition.js"></script>
	<script src="assets/bootstrap/js/bootstrap-alert.js"></script>
	<script src="assets/bootstrap/js/bootstrap-modal.js"></script>
	<script src="assets/bootstrap/js/bootstrap-dropdown.js"></script>
	<script src="assets/bootstrap/js/bootstrap-scrollspy.js"></script>
	<script src="assets/bootstrap/js/bootstrap-tab.js"></script>
	<script src="assets/bootstrap/js/bootstrap-tooltip.js"></script>
	<script src="assets/bootstrap/js/bootstrap-popover.js"></script>
	<script src="assets/bootstrap/js/bootstrap-button.js"></script>
	<script src="assets/bootstrap/js/bootstrap-collapse.js"></script>
	<script src="assets/bootstrap/js/bootstrap-carousel.js"></script>
	<script src="assets/bootstrap/js/bootstrap-typeahead.js"></script>
	<script src="assets/bootstrap/js/bootstrap-affix.js"></script>

	<script src="assets/bootstrap/js/holder/holder.js"></script>
	<script src="assets/bootstrap/js/google-code-prettify/prettify.js"></script>
	<script src="assets/bootstrap/js/application.js"></script>
</body>
</html>