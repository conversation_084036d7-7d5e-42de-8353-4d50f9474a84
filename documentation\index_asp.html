<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<head lang="en">
	<meta http-equiv="content-type" content="text/html;charset=utf-8">
	<title> Documentation - Color Admin</title>
	<!-- Bootstrap styles -->
	<link href="assets/bootstrap/css/bootstrap.css" rel="stylesheet">
	<link href="assets/bootstrap/css/bootstrap-responsive.css" rel="stylesheet">
	<link href="assets/bootstrap/css/docs.css" rel="stylesheet">
	<link href="assets/bootstrap/js/google-code-prettify/prettify.css" rel="stylesheet">

	<!-- Le HTML5 shim, for IE6-8 support of HTML5 elements -->
	<!--[if lt IE 9]>
		<script src="assets/js/html5shiv.js"></script>
	<![endif]-->
</head>
<body data-spy="scroll" data-target=".bs-docs-sidebar">
	<div class="navbar navbar-inverse navbar-page">
		<div class="navbar-inner">
			<div class="container">
				<button type="button" class="btn btn-navbar collapsed" data-toggle="collapse" data-target=".nav-collapse">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>
				<a class="brand" href="#">Admin Template</a>
				<div class="nav-collapse collapse">
					<ul class="nav">
						<li class="">
							<a href="index.html">Design Template</a>
						</li>
						<li class="">
							<a href="index_ajax.html">Ajax Version</a>
						</li>
						<li class="">
							<a href="index_angular_1x.html">Angular 1.x</a>
						</li>
						<li class="">
							<a href="index_angular_13.html">Angular 13.0</a>
						</li>
						<li class="">
							<a href="index_laravel.html">Laravel Version</a>
						</li>
						<li class="">
							<a href="index_vue.html">Vue Version</a>
						</li>
						<li class="">
							<a href="index_react.html">React Version</a>
						</li>
						<li class="active">
							<a href="index_asp.html">ASP.NET</a>
						</li>
						<li>
							<a href="index_change_log.html">Change Log</a>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
	<header class="jumbotron subhead" id="overview">
		<div class="container">
			<h1 class="text-center">Color Admin</h1>
			<p class="lead text-center">&ldquo;ASP.NET Core 5.0 MVC Version&rdquo; Documentation by &ldquo;Sean Ngu&rdquo; v5.1.4</p>
		</div>
		<div class="jumbotron-cover"></div>
	</header>
	<div class="container">
		<div class="row">
			<div class="span12">
				<div class="well with-cover">
					<div class="well-cover" style="background-image: url(assets/images/dotnet.png); background-size: auto 80%; background-position: center; background-repeat: no-repeat; background-color: #fff;"></div>
					<p>
						<strong>
							Last Updated: 13/February/2022<br>
							By: Sean Ngu<br>
							Email: <a href="mailto:<EMAIL>"><EMAIL></a>
						</strong>
					</p>
					<p>
						Thank you for purchasing my theme. If you have any questions that are beyond the scope of this help file,
						please feel free to email your question to my email <a href="mailTo:<EMAIL>"><EMAIL></a>. Thanks so much!
					</p>
			
				</div>
			</div><!-- end span12 -->
		</div><!-- end row -->
		<div class="row">
			<div class="span3 bs-docs-sidebar">
				<ul class="nav nav-list bs-docs-sidenav affix-top">
					<li><a href="#installation"><i class="icon-chevron-right"></i>Installation</a></li>
					<li><a href="#fileStructure"><i class="icon-chevron-right"></i>File Structure</a></li>
					<li><a href="#page-structure"><i class="icon-chevron-right"></i>Page Structure</a></li>
					<li><a href="#page-css"><i class="icon-chevron-right"></i>CSS</a></li>
					<li><a href="#page-javascript"><i class="icon-chevron-right"></i>Javascript</a></li>
					<li><a href="#page-options"><i class="icon-chevron-right"></i>Page Options</a></li>
					<li><a href="#page-dark-mode"><i class="icon-chevron-right"></i>Dark Mode <span class="label">NEW</span></a></li>
					<li><a href="#page-switch-design"><i class="icon-chevron-right"></i>Switch Design <span class="label">NEW</span></a></li>
					<li><a href="#page-gulp-compilation"><i class="icon-chevron-right"></i>Gulp Compilation</a></li>
					<li><a href="#npm-package"><i class="icon-chevron-right"></i>NPM Package</a></li>
				</ul>
			</div>
			<div class="span9">
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="installation"><strong>A) Installation</strong> - <a href="#top">top</a></h3>
						</div>
						<p>
							Generate the css & plugins file from gulp command
						</p>
<pre class="prettyprint linenums">
cd /your-path-url/template_asp/ColorAdmin/

npm install --force
gulp all-css
gulp plugins
</pre>
						<hr />
						<p>
							Download the .NET Core SDK from <a href="https://dotnet.microsoft.com/download" target="_blank">official website</a> and install on your machine.
						</p>
						<p>If you are using <b>dotnet cli</b>, run the following command:</p>
<pre class="prettyprint linenums">
cd /your-path-url/template_asp/ColorAdmin/

donet run
</pre>
						<div style="margin-bottom: 15px;"><b>OR</b></div>
						<p>
							If you are using visual studio, just double click the <code>ColorAdmin.sln</code> inside the template folder. You may download the latest version of Visual studio from <a href="https://visualstudio.microsoft.com/downloads/" target="_blank">here</a>.
						</p>
						<hr />
						<p>Copy over the required image from global <code>assets</code> folder</p>
<pre class="prettyprint linenums">
&lt;!-- copy the following folder--&gt;
/admin/template/assets/img
 
&lt;!-- paste it into asp folder --&gt;
/admin/template/template_asp/ColorAdmin/wwwroot/img
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="fileStructure"><strong>B) File Structure</strong> - <a href="#top">top</a></h3>
						</div>
						<p>File structure overview for .NET Core 5.0 MVC Version</p>
						
<pre class="prettyprint linenums">
template_asp/
├── ColorAdmin.sln        
└── ColorAdmin/
    ├── Areas/                        // ASP Identity Files
    ├── bin/
    ├── Controllers/
    ├── Data/
    ├── Models/
    ├── obj/
    ├── Properties/
    ├── src/                          // scss / img / js source file
    ├── Views/
    ├── wwwroot/                      // generated css / js / img file
    ├── app.db
    ├── Program.cs
    ├── Startup.cs
    ├── appsettings.development.json
    ├── appsettings.json
    ├── package.json
    ├── sidebar.json                  // sidebar menu structure
    ├── ScaffoldingReadMe.txt
    ├── gulpfile.js                   // gulp setting files
    └── ColorAdmin.csproj
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-structure"><strong>C) Page Structure</strong> - <a href="#top">top</a></h3>
						</div>
						<p>Below is the general page structure for <code>/Views/Shared/Layout.cshtml</code>.</p>
<pre class="prettyprint linenums">
&lt;!DOCTYPE html&gt;
&lt;html lang="en" @ViewData["AppHtmlAttr"]&gt;
&lt;head&gt;
  &lt;meta charset="utf-8" /&gt;
  &lt;title&gt;Color Admin | @ViewData["Title"]&lt;/title&gt;
  &lt;meta name="viewport" content="width=device-width, initial-scale=1" /&gt;
  &lt;meta name="description" content="@ViewData["MetaDescription"]" /&gt;
  &lt;meta name="author" content="@ViewData["MetaAuthor"]" /&gt;
  &lt;meta name="keywords" content="@ViewData["MetaKeywords"]" /&gt;

  @RenderSection("MetaTag", required: false)

  &lt;!-- ================== BEGIN core-css ================== --&gt;
  &lt;link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" /&gt;
  &lt;link href="~/css/vendor.min.css" rel="stylesheet" /&gt;
  &lt;link href="~/css/default/app.min.css" rel="stylesheet" /&gt;
  &lt;!-- ================== END core-css ================== --&gt;

  @RenderSection("Styles", required: false)
&lt;/head&gt;
&lt;body class="@ViewData["AppBodyClass"]"&gt;
  &lt;!-- BEGIN #app --&gt;
  &lt;div id="app" class="app @(ViewData["AppSidebarNone"] == null ? "app-sidebar-fixed" : "") @(ViewData["AppHeaderNone"] == null ? "app-header-fixed" : "") @ViewData["AppClass"]"&gt;
    @if (ViewData["AppHeaderNone"] == null) {
      &lt;partial name="_Header" /&gt;
    }
    
    @if (ViewData["AppTopMenu"] != null) {
      &lt;partial name="_TopMenu" /&gt;
    }

    @if (ViewData["AppSidebarNone"] == null) {
      &lt;partial name="_Sidebar" /&gt;
    }
    
    @if (ViewData["AppSidebarTwo"] != null) {
      &lt;partial name="_SidebarRight" /&gt;
    }

    @if (ViewData["AppEmpty"] == null) {
      &lt;div id="content" class="app-content @ViewData["AppContentClass"]" @ViewData["AppContentAttr"]&gt;
        @RenderBody()
      &lt;/div&gt;
    } else {
      @RenderBody()
    }

    @RenderSection("AppOutterContent", required: false)
  &lt;/div&gt;
  &lt;!-- END #app --&gt;

  &lt;!-- ================== BEGIN core-js ================== --&gt;
  &lt;script src="~/js/vendor.min.js"&gt;&lt;/script&gt;
  &lt;script src="~/js/app.min.js"&gt;&lt;/script&gt;
  &lt;!-- ================== END core-js ================== --&gt;
  @RenderSection("Scripts", required: false)
&lt;/body&gt;
&lt;/html&gt;
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-css"><strong>D) CSS</strong> - <a href="#top">top</a></h3>
						</div>
						<p>All the css plugin files used in ColorAdmin has been compiled into one single files <code>vendor.min.css</code> by using gulp. Below is the list of library included in vendor.min.css. You may change the setting in <code>gulpfile.js</code> if you wish to add / remove library from vendor.min.css.</p>
						<ol>
							<li>FontAwesome</li>
							<li>jQuery UI</li>
							<li>Animate.css</li>
							<li>Pace Loader</li>
							<li>Perfect Scrollbar</li>
						</ol>
						<hr />
						<p>For <code>app.min.css</code>, it will be the core css file for Color Admin which combine bootstrap 5 scss compiled by using gulp as well. You may check the source code from <code>/admin/src/scss/default/</code></p>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-javascript"><strong>E) Javascript</strong> - <a href="#top">top</a></h3>
						</div>
						<p>This theme compiles 6 javascript library into one single file <code>vendor.min.js</code> by using gulp. Below is the list of library included in app.min.js. You may change the setting in <code>gulpfile.js</code> if you wish to add / remove library from vendor.min.js.</p>
						<ol>
							<li>Pace Loader</li>
							<li>jQuery</li>
							<li>jQuery UI</li>
							<li>Bootstrap</li>
							<li>Perfect Scrollbar</li>
							<li>JavaScript Cookie</li>
						</ol>
						<hr />
						<p>For <code>app.min.js</code>, it will be the core js file for Color Admin which compiled by using gulp as well. You may check the source code from <code>/admin/src/js/app.js</code></p>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-options"><strong>F) Page Options</strong> - <a href="#top">top</a></h3>
						</div>
						<p>Below is the list of ViewData you can use to set the application layout</p>
<pre class="prettyprint linenums">
@{
  @ViewData["Title"]            = "Page Title";
  @ViewData["AppHeaderNone"]    = "true";
  @ViewData["AppHeaderInverse"] = "true";
  @ViewData["AppTopMenu"]       = "true";
  @ViewData["AppSidebarNone"]   = "true";
  @ViewData["AppSidebarTwo"]    = "true";
  @ViewData["AppSidebarTransparent"] = "true";
  @ViewData["AppEmpty"]         = "true";
  @ViewData["AppClass"]         = "-- css class --";
  @ViewData["AppBodyClass"]     = "-- css class --";
  @ViewData["AppContentClass"]  = "-- css class --";
  @ViewData["AppContentAttr"]   = "-- data attribute --";
  @ViewData["AppHtmlAttr"]      = "-- data attribute --";
}
</pre>
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-dark-mode"><strong>G) Dark Mode</strong> - <a href="#top">top</a></h3>
						</div>
						<p>Add the <code>.dark-mode</code> class to <code>&lt;html&gt;</code> in <code>template_asp/ColorAdmin/Views/Shared/_Layout.cshtml</code> order to enable the dark mode.</p>
						<div class="alert alert-info">
							If you have <b>pre-select</b> the dark-mode option in theme panel, do not forget to <b>clear</b> the browser cookie or <b>remove</b> the theme panel so that it won't affect the dark-mode class while page load.
						</div>
<pre class="prettyprint linenums">
&lt;html lang="en" class="dark-mode" @ViewData["AppHtmlAttr"]>&gt;
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-switch-design"><strong>H) Switch Design</strong> - <a href="#top">top</a></h3>
						</div>
						<h4 style="margin-bottom: 15px">Apple Design</h4>
						<ol>
							<li>
								<div style="padding-bottom: 5px;">Change the base css file from default to apple in <code>template_asp/ColorAdmin/Views/Shared/_Layout.cshtml</code>.</div>							
<pre class="prettyprint linenums">
&lt;link href="~/css/apple/app.min.css" rel="stylesheet" /&gt;
&lt;link href="~/lib/ionicons/css/ionicons.min.css" rel="stylesheet" /&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Change the sidebar icon from Fontawesome to Ionicons and background color class is needed as well.</div>
<pre class="prettyprint linenums">
&lt;div class="menu-icon"&gt;
  &lt;i class="ion-ios-pulse bg-gradient-blue"&gt;&lt;/i&gt;
&lt;/div&gt;
</pre>
							</li>
						</ol>
						<hr style="margin-top: 30px" />
						<h4 style="margin-bottom: 15px">Facebook Design</h4>
						<ol>
							<li>
								<div style="padding-bottom: 5px;">Change the base css file from default to facebook in <code>template_asp/ColorAdmin/Views/Shared/_Layout.cshtml</code>.</div>							
<pre class="prettyprint linenums">
&lt;link href="~/css/facebook/app.min.css" rel="stylesheet" /&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Add the class <code>.app-header-inverse</code> to the <code>.app-header</code> container in <code>template_asp/ColorAdmin/Views/Shared/_Header.cshtml</code>.</div>
<pre class="prettyprint linenums">
&lt;div id="header" class="app-header app-header-inverse"&gt;
  ...
&lt;/div&gt;
</pre>
							</li>
						</ol>
						<hr style="margin-top: 30px" />
						<h4 style="margin-bottom: 15px">Transparent Design</h4>
						<ol>
							<li>
								<div style="padding-bottom: 5px;">Change the base css file from default to facebook in <code>template_asp/ColorAdmin/Views/Shared/_Layout.cshtml</code>.</div>							
<pre class="prettyprint linenums">
&lt;link href="~/css/transparent/app.min.css" rel="stylesheet" /&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Add the <code>.app-cover</code> next to the <code>&lt;body&gt;</code> tag in <code>template_asp/ColorAdmin/Views/Shared/_Layout.cshtml</code>.</div>
<pre class="prettyprint linenums">
&lt;body class="@ViewData["AppBodyClass"]"&gt;
  &lt;!-- BEGIN page-cover --&gt;
  &lt;div class="app-cover"&gt;&lt;/div&gt;
  &lt;!-- END page-cover --&gt;
  
  ...
&lt;/body&gt;
</pre>
							</li>
						</ol>
						<hr style="margin-top: 30px" />
						<h4 style="margin-bottom: 15px">Google Design</h4>
						<ol>
							<li>
								<div style="padding-bottom: 5px;">Change the base css file from default to facebook in <code>template_asp/ColorAdmin/Views/Shared/_Layout.cshtml</code>.</div>							
<pre class="prettyprint linenums">
&lt;link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900" rel="stylesheet" /&gt;
&lt;link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" /&gt;
&lt;link href="~/css/google/app.min.css" rel="stylesheet" /&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Change the sidebar icon from Fontawesome to Material Icons.</div>
<pre class="prettyprint linenums">
&lt;div class="menu-icon"&gt;
  &lt;i class="material-icons"&gt;home&lt;/i&gt;
&lt;/div&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Add the class <code>.app-with-wide-sidebar</code>, <code>.app-with-light-sidebar</code> to the <code>.app</code> container in <code>template_asp/ColorAdmin/Views/Shared/_Layout.cshtml</code>.</div>
<pre class="prettyprint linenums">
&lt;div id="app" class="app app-header-fixed app-sidebar-fixed app-with-wide-sidebar app-with-light-sidebar"&gt;
  ...
&lt;/div&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Add the navbar desktop toggler to the <code>.app-header</code>  in <code>template_asp/ColorAdmin/Views/Shared/_Header.cshtml</code>.</div>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #header --&gt;
&lt;div id="header" class="app-header"&gt;
  &lt;!-- BEGIN navbar-header --&gt;
  &lt;div class="navbar-header"&gt;
    &lt;button type="button" class="navbar-desktop-toggler" data-toggle="app-sidebar-minify"&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
    &lt;/button&gt;
    &lt;button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-mobile"&gt;
      ...
    &lt;/button&gt;
    &lt;a href="/" class="navbar-brand"&gt;
      Color Admin
    &lt;/a&gt;
  &lt;/div&gt;
  &lt;!-- END navbar-header --&gt;
  ...
&lt;/div&gt;
</pre>
							</li>
						</ol>
						<hr style="margin-top: 30px" />
						<h4 style="margin-bottom: 15px">Material Design</h4>
						<ol>
							<li>
								<div style="padding-bottom: 5px;">Change the base css file from default to material in <code>template_asp/ColorAdmin/Views/Shared/_Layout.cshtml</code>.</div>							
<pre class="prettyprint linenums">
&lt;link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900" rel="stylesheet" /&gt;
&lt;link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" /&gt;
&lt;link href="~/css/material/app.min.css" rel="stylesheet" /&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Change the sidebar icon from Fontawesome to Material Icons.</div>
<pre class="prettyprint linenums">
&lt;div class="menu-icon"&gt;
  &lt;i class="material-icons"&gt;home&lt;/i&gt;
&lt;/div&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Add the class <code>.app-with-wide-sidebar</code> to the <code>.app</code> container in <code>template_asp/ColorAdmin/Views/Shared/_Layout.cshtml</code>.</div>
<pre class="prettyprint linenums">
&lt;div id="app" class="app app-header-fixed app-sidebar-fixed app-with-wide-sidebar"&gt;
  ...
&lt;/div&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Add the navbar desktop toggler to the <code>.app-header</code>  in <code>template_asp/ColorAdmin/Views/Shared/_Header.cshtml</code>.</div>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #header --&gt;
&lt;div id="header" class="app-header"&gt;
  &lt;!-- BEGIN navbar-header --&gt;
  &lt;div class="navbar-header"&gt;
    &lt;button type="button" class="navbar-desktop-toggler" data-toggle="app-sidebar-minify"&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
      &lt;span class="icon-bar"&gt;&lt;/span&gt;
    &lt;/button&gt;
    &lt;button type="button" class="navbar-mobile-toggler" data-toggle="app-sidebar-mobile"&gt;
      ...
    &lt;/button&gt;
    &lt;a href="index.html" class="navbar-brand"&gt;
      Color Admin Material
    &lt;/a&gt;
  &lt;/div&gt;
  &lt;!-- END navbar-header --&gt;
  ...
&lt;/div&gt;
</pre>
							</li>
							
							<li>
								<div style="padding-bottom: 5px">Add the floating navbar form to the <code>.app-header</code>  in <code>template_asp/ColorAdmin/Views/Shared/_Header.cshtml</code> AND <b>REMOVE</b> the default <code>.navbar-form</code>.</div>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #header --&gt;
&lt;div id="header" class="app-header"&gt;
  &lt;!-- BEGIN header-nav --&gt;
  &lt;div class="navbar-nav"&gt;
    &lt;div class="navbar-item"&gt;
      &lt;a href="#" data-toggle="app-header-floating-form" class="navbar-link icon"&gt;
        &lt;i class="material-icons"&gt;search&lt;/i&gt;
      &lt;/a&gt;
      
      &lt;!-- REMOVE IT --&gt;
      &lt;div class="navbar-item navbar-form"&gt;
        ...
      &lt;/div&gt;
    &lt;/div&gt;
    ...
  &lt;/div&gt;
  &lt;!-- END header-nav --&gt;
  
  &lt;div class="navbar-floating-form"&gt;
    &lt;button class="search-btn" type="submit"&gt;&lt;i class="material-icons"&gt;search&lt;/i&gt;&lt;/button&gt;
    &lt;input type="text" class="form-control" placeholder="Search Something..." /&gt;
    &lt;a href="#" class="close" data-dismiss="app-header-floating-form"&gt;
      &lt;i class="material-icons"&gt;close&lt;/i&gt;
    &lt;/a&gt;
  &lt;/div&gt;
&lt;/div&gt;
</pre>
							</li>
							<li>
								<div style="padding-bottom: 5px">Change the <code>.app-loader</code>  in <code>template_asp/ColorAdmin/Views/Shared/_Layout.cshtml</code>.</div>
<pre class="prettyprint linenums">
&lt;!-- BEGIN #loader --&gt;
&lt;div id="loader" class="app-loader"&gt;
  &lt;div class="material-loader"&gt;
    &lt;svg class="circular" viewBox="25 25 50 50"&gt;
      &lt;circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10"&gt;&lt;/circle&gt;
    &lt;/svg&gt;
    &lt;div class="message"&gt;Loading...&lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;
&lt;!-- END #loader --&gt;
</pre>
							</li>
						</ol>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="page-gulp-compilation"><strong>I) Gulp Compilation</strong> - <a href="#top">top</a></h3>
						</div>
						<p>
							If you wish to enable the gulp compilation during debug / release process, kindly enable the following option in <code>ColorAdmin.csproj</code>.
						</p>
						<div class="alert alert-info">
							Do not forget to run the <b>npm install</b> command by using command prompt / terminal to generate the <code>node_module</code> folder.
						</div>
<pre class="prettyprint linenums">
// remove the comment syntax
 
&lt;!--
&lt;Target Name="MyPreCompileTarget" BeforeTargets="Build"&gt;
  &lt;Exec Command="gulp" /&gt;
&lt;/Target&gt;
--&gt;
</pre>
					</div><!-- end span12 -->
				</div><!-- end row-fluid -->
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="npm-package"><strong>J) NPM Package</strong> - <a href="#top">top</a></h3>
						</div>
						<p>
							Below is the list of package that has been installed in this project. You may use the following example to find the package from their official website.
							<code>https://www.npmjs.com/package/</code><code>reactstrap</code>
						</p>
<pre class="prettyprint linenums">
{
  "name": "color-admin-default-theme",
  "version": "5.1.3",
  "description": "Color Admin - Default Theme",
  "private": true,
  "main": "index.js",
  "scripts": {
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "author": "SeanTheme",
  "license": "ISC",
  "devDependencies": {
    "gulp": "^4.0.2",
    "gulp-autoprefixer": "^8.0.0",
    "gulp-clean-css": "^4.3.0",
    "gulp-concat": "^2.6.1",
    "gulp-connect": "^5.7.0",
    "gulp-csso": "^4.0.1",
    "gulp-download2": "^1.1.0",
    "gulp-file-include": "^2.3.0",
    "gulp-header": "^2.0.9",
    "gulp-less": "^5.0.0",
    "gulp-livereload": "^4.0.2",
    "gulp-sass": "^5.0.0",
    "gulp-sass-variables": "^1.2.0",
    "gulp-sourcemaps": "^3.0.0",
    "gulp-uglify": "^3.0.2",
    "gulp-uglify-es": "^3.0.0",
    "sass": "^1.44.0"
  },
  "dependencies": {
    "@fortawesome/fontawesome-free": "^6.0.0-beta2",
    "@fullcalendar/bootstrap": "^5.10.1",
    "@fullcalendar/core": "^5.10.1",
    "@fullcalendar/daygrid": "^5.10.1",
    "@fullcalendar/interaction": "^5.10.1",
    "@fullcalendar/list": "^5.10.1",
    "@fullcalendar/timegrid": "^5.10.1",
    "@highlightjs/cdn-assets": "^11.3.1",
    "abpetkov-powerange": "github:npmcomponent/abpetkov-powerange",
    "angular": "^1.8.2",
    "angular-ui-bootstrap": "^2.5.6",
    "angular-ui-router": "^1.0.30",
    "animate.css": "^4.1.1",
    "apexcharts": "^3.31.0",
    "blueimp-file-upload": "^10.32.0",
    "blueimp-gallery": "^3.4.0",
    "bootstrap": "^5.1.3",
    "bootstrap-datepicker": "^1.9.0",
    "bootstrap-daterangepicker": "^3.1.0",
    "bootstrap-datetime-picker": "^2.4.4",
    "bootstrap-icons": "^1.7.1",
    "bootstrap-social": "^5.1.1",
    "bootstrap-timepicker": "^0.5.2",
    "bootstrap3-wysihtml5-bower": "^0.3.3",
    "chart.js": "^3.6.1",
    "ckeditor": "^4.12.1",
    "clipboard": "^2.0.8",
    "d3": "^3.5.17",
    "datatables.net": "^1.11.3",
    "datatables.net-autofill": "^2.3.9",
    "datatables.net-autofill-bs5": "^2.3.9",
    "datatables.net-bs5": "^1.11.3",
    "datatables.net-buttons": "^2.0.1",
    "datatables.net-buttons-bs5": "^2.0.1",
    "datatables.net-colreorder": "^1.5.5",
    "datatables.net-colreorder-bs5": "^1.5.5",
    "datatables.net-fixedcolumns": "^4.0.1",
    "datatables.net-fixedcolumns-bs5": "^4.0.1",
    "datatables.net-fixedheader": "^3.2.0",
    "datatables.net-fixedheader-bs5": "^3.2.0",
    "datatables.net-keytable": "^2.6.4",
    "datatables.net-keytable-bs5": "^2.6.4",
    "datatables.net-responsive": "^2.2.9",
    "datatables.net-responsive-bs5": "^2.2.9",
    "datatables.net-rowreorder": "^1.2.8",
    "datatables.net-rowreorder-bs5": "^1.2.8",
    "datatables.net-scroller": "^2.0.5",
    "datatables.net-scroller-bs5": "^2.0.5",
    "datatables.net-select": "^1.3.3",
    "datatables.net-select-bs5": "^1.3.3",
    "dropzone": "^5.9.3",
    "flag-icon-css": "^4.1.6",
    "flot": "^4.2.2",
    "gritter": "^1.7.4",
    "intro.js": "^4.3.0",
    "ion-rangeslider": "^2.3.1",
    "isotope-layout": "^3.0.6",
    "jquery": "^3.6.0",
    "jquery-knob": "^1.2.11",
    "jquery-migrate": "^3.3.2",
    "jquery-mockjax": "^2.6.0",
    "jquery-slimscroll": "^1.3.8",
    "jquery-sparkline": "^2.4.0",
    "jquery-ui-dist": "^1.12.1",
    "jquery.maskedinput": "^1.4.1",
    "js-cookie": "^3.0.1",
    "jstree": "^3.3.12",
    "jszip": "^3.7.1",
    "jvectormap-next": "^3.1.1",
    "lightbox2": "^2.11.3",
    "lity": "^2.4.1",
    "masonry-layout": "^4.2.2",
    "merge-stream": "^2.0.0",
    "moment": "^2.29.1",
    "nvd3": "^1.8.6",
    "oclazyload": "^1.1.0",
    "pace-js": "^1.2.4",
    "parsleyjs": "^2.9.2",
    "pdfmake": "^0.2.4",
    "perfect-scrollbar": "^1.5.3",
    "popper.js": "^1.16.1",
    "raphael": "^2.3.0",
    "select-picker": "^0.3.2",
    "select2": "^4.0.13",
    "simple-line-icons": "^2.5.5",
    "spectrum-colorpicker2": "^2.0.8",
    "summernote": "^0.8.20",
    "sweetalert": "^2.1.2",
    "swiper": "^7.3.1",
    "switchery": "github:abpetkov/switchery",
    "tag-it": "^2.0.0",
    "x-editable-bs4": "^1.5.4"
  }
}
</pre>
					</div>
				</div><!-- end row-fluid -->
			</div><!-- end span12 -->
		</div><!-- end row-fluid -->
	</div><!-- end container -->
	
	<footer class="footer">
		<div class="container text-left">
			<p>Once again, thank you so much for purchasing this theme. As I said at the beginning, I'd be glad to help you if you have any questions relating to this theme. No guarantees, but I'll do my best to assist. If you have a more general question relating to the themes, you might consider visiting the forums and asking your question via <a href="mailTo:<EMAIL>">email</a>.</p> 
			<br />
			<p class="append-bottom alt large"><strong>Sean Ngu</strong></p>
			<p><a href="#top">Go To Table of Contents</a></p>
		</div>
	</footer><!-- end footer -->
	
	<script src="assets/bootstrap/js/jquery.js"></script>
	<script src="assets/bootstrap/js/bootstrap-transition.js"></script>
	<script src="assets/bootstrap/js/bootstrap-alert.js"></script>
	<script src="assets/bootstrap/js/bootstrap-modal.js"></script>
	<script src="assets/bootstrap/js/bootstrap-dropdown.js"></script>
	<script src="assets/bootstrap/js/bootstrap-scrollspy.js"></script>
	<script src="assets/bootstrap/js/bootstrap-tab.js"></script>
	<script src="assets/bootstrap/js/bootstrap-tooltip.js"></script>
	<script src="assets/bootstrap/js/bootstrap-popover.js"></script>
	<script src="assets/bootstrap/js/bootstrap-button.js"></script>
	<script src="assets/bootstrap/js/bootstrap-collapse.js"></script>
	<script src="assets/bootstrap/js/bootstrap-carousel.js"></script>
	<script src="assets/bootstrap/js/bootstrap-typeahead.js"></script>
	<script src="assets/bootstrap/js/bootstrap-affix.js"></script>

	<script src="assets/bootstrap/js/holder/holder.js"></script>
	<script src="assets/bootstrap/js/google-code-prettify/prettify.js"></script>
	<script src="assets/bootstrap/js/application.js"></script>
</body>
</html>