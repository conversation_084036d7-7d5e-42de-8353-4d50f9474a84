@@if(context.theme == 'transparent' && !context.withoutCover) 
{<!-- BEGIN page-cover -->
	<div class="app-cover"></div>
	<!-- END page-cover -->
	
	}<!-- BEGIN #loader -->
	<div id="loader" class="app-loader">
		@@if (context.theme != 'material')
		{<span class="spinner"></span>}@@if (context.theme == 'material')
		{<div class="material-loader">
			<svg class="circular" viewBox="25 25 50 50">
				<circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10"></circle>
			</svg>
			<div class="message">Loading...</div>
		</div>}
	</div>
	<!-- END #loader -->