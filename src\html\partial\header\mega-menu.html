<!-- BEGIN navbar-collapse -->
			<div class="collapse d-md-block me-auto" id="top-navbar">
				<div class="navbar-nav">
					<div class="navbar-item dropdown dropdown-lg">
						<a href="#" class="navbar-link dropdown-toggle d-flex align-items-center" data-bs-toggle="dropdown">@@if(context.theme == 'material') {
							<i class="material-icons fs-20px me-2 text-blue">apps</i>}@@if(context.theme != 'material') {
							<i class="fa fa-th-large fa-fw me-1"></i> }
							<span class="d-lg-inline d-md-none">Mega</span>
							<b class="caret ms-1"></b>
						</a>
						<div class="dropdown-menu dropdown-menu-lg@@if(context.theme!='transparent'){ text-gray-900}">
							<div class="row">
								<div class="col-lg-4 col-md-4">
									<div class="h5@@if(context.theme != 'google'){ fw-bolder} mb-2">UI Kits</div>
									<div class="row mb-3">
										<div class="col-lg-6">
											<ul class="nav d-block@@if(context.theme != 'material' && context.theme != 'google'){ fw-bold}">
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> FontAwesome</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Ionicons</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Simple Line Icons</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Typography</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Media Object</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Widget Boxes</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Tabs & Accordions</a></li>
											</ul>
										</div>
										<div class="col-lg-6">
											<ul class="nav d-block@@if(context.theme != 'material' && context.theme != 'google'){ fw-bold}">
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Unlimited Nav Tabs</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Modal & Notification</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Buttons</a></li>
											</ul>
										</div>
									</div>
								</div>
								<div class="col-lg-4 col-md-4">
									<div class="h5@@if(context.theme != 'google'){ fw-bolder} mb-2">Page Options <span class="badge bg-pink ms-2">11</span></div>
									<div class="row">
										<div class="col-lg-6">
											<ul class="nav d-block@@if(context.theme != 'material' && context.theme != 'google'){ fw-bold}">
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Blank Page</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Page with Footer <span class="badge bg-success py-1">NEW</span></a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Page without Sidebar</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Page with Right Sidebar</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Page with Minified Sidebar</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Page with Two Sidebar</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Page with Line Icons</a></li>
											</ul>
										</div>
										<div class="col-lg-6">
											<ul class="nav d-block@@if(context.theme != 'material' && context.theme != 'google'){ fw-bold}">
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Full Height Content</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Page with Mega Menu</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Page with Light Sidebar</a></li>
												<li><a href="javascript:;" class="text-ellipsis @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white} text-decoration-none"><i class="fa fa-chevron-right fa-fw text-gray-500"></i> Page with Large Sidebar</a></li>
											</ul>
										</div>
									</div>
								</div>
								<div class="col-lg-4 col-md-4@@if(context.theme != 'google'){ fw-bold}">
									<div class="h5@@if(context.theme != 'google'){ fw-bolder} mb-2">Paragraph</div>
									<p>
										Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis libero purus, fermentum at libero convallis, auctor dignissim mauris. Nunc laoreet pellentesque turpis sodales ornare. Nunc vestibulum nunc lorem, at sodales velit malesuada congue. Nam est tortor, tincidunt sit amet eros vitae, aliquam finibus mauris.
									</p>
									<p>
										Fusce ac ligula laoreet ante dapibus mattis. Nam auctor vulputate aliquam. Suspendisse efficitur, felis sed elementum eleifend, ipsum tellus sodales nisi, ut condimentum nisi sem in nibh. Phasellus suscipit vulputate purus at venenatis. Quisque luctus tincidunt tempor.
									</p>
								</div>
							</div>
						</div>
					</div>
					<div class="navbar-item">
						<a href="javascript:;" class="navbar-link d-flex align-items-center">@@if(context.theme == 'material') {
							<i class="material-icons fs-20px me-2">people</i>}@@if(context.theme != 'material') {
							<i class="fa fa-gem fa-fw me-1"></i>}
							<span class="d-lg-inline d-md-none">Client</span>
						</a>
					</div>
					<div class="navbar-item dropdown">
						<a href="#" class="navbar-link dropdown-toggle d-flex align-items-center" data-bs-toggle="dropdown">@@if(context.theme == 'material') {
							<i class="material-icons fs-20px me-2">create</i>}@@if(context.theme != 'material') {
							<i class="fa fa-database fa-fw me-1"></i>}
							<span class="d-lg-inline d-md-none">New</span>
							 <b class="caret ms-1"></b>
						</a>
						<div class="dropdown-menu">
							<a href="javascript:;" class="dropdown-item">Action</a>
							<a href="javascript:;" class="dropdown-item">Another action</a>
							<a href="javascript:;" class="dropdown-item">Something else here</a>
							<div class="dropdown-divider"></div>
							<a href="javascript:;" class="dropdown-item">Separated link</a>
							<div class="dropdown-divider"></div>
							<a href="javascript:;" class="dropdown-item">One more separated link</a>
						</div>
					</div>
				</div>
			</div>
			<!-- END navbar-collapse -->