<?php
#region region DOCS
/** @var Whiteboard[] $whiteboards */
/** @var Whiteboard $newwhiteboard */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Whiteboards</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <?php #endregion head ?>

    <style>
        .whiteboard-tools {
            background:#424a50; /* Changed to a slightly darker gray */
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .whiteboard-tools .btn.active {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }

        .whiteboard-canvas-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 600px;
            background: #f8f9fa;
        }

        #whiteboardCanvas {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border-radius: 4px;
        }

        .form-range {
            vertical-align: middle;
        }

        #colorPicker {
            width: 40px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .list-group-item.list-group-item-action:hover { /* Target more specifically for modal items */
            background-color: #424a50; /* Darker gray, same as tools panel */
            color: #ffffff; /* Ensure text is white for contrast */
        }

        .whiteboard-tools .btn-group {
            margin-right: 10px;
        }

        .whiteboard-tools .form-label {
            margin-bottom: 0;
            align-self: center;
        }

        @media (max-width: 768px) {
            .whiteboard-tools .row > div {
                margin-bottom: 10px;
            }

            .whiteboard-tools .text-end {
                text-align: left !important;
            }

            #whiteboardCanvas {
                max-width: 100%;
                height: auto;
            }

            .whiteboard-canvas-container {
                min-height: 400px;
            }

            .btn-group {
                flex-wrap: wrap;
            }
        }

        .cursor-crosshair {
            cursor: crosshair !important;
        }

        .cursor-grab {
            cursor: grab !important;
        }

        .cursor-grab:active {
            cursor: grabbing !important;
        }

        #brushSizeValue{
            margin-top: .16rem !important;
        }

        .color-palette {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .color-btn {
            width: 40px; /* Matched to #colorPicker */
            height: 30px;
            border: none; /* Matched to #colorPicker */
            border-radius: 4px;
            cursor: pointer;
            transition: transform 0.1s;
        }

        .color-btn:hover {
            transform: scale(1.1);
            outline: 1px solid #007bff; /* Using outline for hover feedback */
        }

        .color-btn.active {
            outline: 3px solid #007bff; /* Using outline for active state, similar to original border-width */
        }
        /* Ensure panel heading icon buttons are circular */
        .panel-heading-btn .btn.btn-xs.btn-icon {
            width: 24px; 
            height: 24px !important;
            padding: 0;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            line-height: 1; /* Prevents text from affecting height */
        }
        .panel-heading-btn .btn.btn-xs.btn-icon i {
            font-size: 12px; /* Adjust icon size if needed */
        }

        /* Overwrite modal styling */
        .modal-lg .card {
            border: 1px solid #dee2e6;
        }

        .modal-lg .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        #conflictingName {
            color: #dc3545;
            font-weight: bold;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Current whiteboard indicator styling */
        #currentWhiteboardIndicator {
            border-left: 4px solid #007bff;
            background-color: #e7f3ff;
            border-color: #b8daff;
        }

        #currentWhiteboardIndicator .btn {
            font-size: 0.875rem;
            padding: 0.25rem 0.5rem;
        }

        /* Save modal improvements */
        #currentWhiteboardInfo {
            border-left: 4px solid #17a2b8;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }

        #quickActionButtons .btn {
            font-weight: 500;
        }

        #saveHelpText {
            font-size: 0.875rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <?php #region region HEADER ?>
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>
    <?php #endregion header ?>

    <?php #region region TOPBAR ?>
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>
    <?php #endregion topbar ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN breadcrumb -->
        <ol class="breadcrumb float-xl-end">
            <li class="breadcrumb-item"><a href="javascript:;">Home</a></li>
            <li class="breadcrumb-item active">Whiteboards</li>
        </ol>
        <!-- END breadcrumb -->
        
        <!-- BEGIN page-header -->
        <h1 class="page-header">Whiteboards <small>Digital whiteboard interface</small></h1>
        <!-- Current whiteboard indicator -->
        <div id="currentWhiteboardIndicator" class="alert alert-info" style="display: none;">
            <i class="fa fa-edit"></i> Currently editing: <strong id="currentWhiteboardName"></strong>
            <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="clearCurrentWhiteboard()">
                <i class="fa fa-plus"></i> New Whiteboard
            </button>
        </div>
        <!-- END page-header -->



        <!-- BEGIN whiteboard-container -->
        <div class="row">
            <div class="col-xl-12">
                <!-- BEGIN panel -->
                <div class="panel panel-inverse">
                    <div class="panel-heading">
                        <h4 class="panel-title">Digital Whiteboard</h4>
                        <div class="panel-heading-btn">
                            <a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
                            <a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>                            
                        </div>
                    </div>
                    <div class="panel-body">
                        <!-- BEGIN whiteboard-tools -->
                        <div class="whiteboard-tools mb-3">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="btn-group me-2" role="group">
                                        <button type="button" class="btn btn-primary" id="penTool" title="Pen">
                                            <i class="fa fa-pen"></i>
                                        </button>
                                        <button type="button" class="btn btn-secondary" id="eraserTool" title="Eraser">
                                            <i class="fa fa-eraser"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger" id="clearCanvas" title="Clear All">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </div>
                                    
                                    <div class="btn-group me-2" role="group">
                                        <label class="btn btn-info">
                                            <i class="fa fa-palette"></i>
                                            <input type="color" id="colorPicker" value="#000000" style="display: none;">
                                        </label>
                                        <div class="color-palette ms-2">
                                            <div class="color-btn" data-color="#ff0000" style="background-color: #ff0000;" title="Red"></div>
                                            <div class="color-btn" data-color="#00ff00" style="background-color: #00ff00;" title="Green"></div>
                                            <div class="color-btn" data-color="#0000ff" style="background-color: #0000ff;" title="Blue"></div>
                                            <div class="color-btn" data-color="#ff00ff" style="background-color: #ff00ff;" title="Fuchsia"></div>
                                            <div class="color-btn active" data-color="#000000" style="background-color: #000000;" title="Black"></div>
                                            <div class="color-btn" data-color="#ffff00" style="background-color: #ffff00;" title="Yellow"></div>
                                            <div class="color-btn" data-color="#ffa500" style="background-color: #ffa500;" title="Orange"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="btn-group me-2" role="group">
                                        <label for="brushSize" class="form-label me-2">Size:</label>
                                        <input type="range" id="brushSize" min="1" max="50" value="6" class="form-range" style="width: 100px;">
                                        <span id="brushSizeValue" class="ms-2">6</span>
                                    </div>                                    <div class="btn-group me-2" role="group" aria-label="Canvas Size">
                                        <input type="number" id="canvasWidth" class="form-control form-control-sm" value="1800" min="100" max="5000" style="width: 70px;" title="Canvas Width">
                                        <span class="input-group-text input-group-text-sm py-0">x</span>
                                        <input type="number" id="canvasHeight" class="form-control form-control-sm" value="700" min="100" max="5000" style="width: 70px;" title="Canvas Height">
                                        <button type="button" class="btn btn-secondary btn-sm" id="applyCanvasSize">Apply</button>
                                    </div>
                                    <div class="btn-group" role="group" aria-label="Preset Sizes">
                                        <button type="button" class="btn btn-secondary btn-sm preset-size" data-width="800" data-height="600">800x600</button>
                                        <button type="button" class="btn btn-secondary btn-sm preset-size" data-width="1024" data-height="768">1024x768</button>
                                        <button type="button" class="btn btn-secondary btn-sm preset-size" data-width="1200" data-height="800">1200x800</button>
                                        <button type="button" class="btn btn-secondary btn-sm preset-size" data-width="1920" data-height="1080">FHD</button>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#saveModal">
                                        <i class="fa fa-save"></i> Save
                                    </button>
                                    <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#loadModal">
                                        <i class="fa fa-folder-open"></i> Load
                                    </button>
                                    <button type="button" class="btn btn-info" id="downloadCanvas">
                                        <i class="fa fa-download"></i> Download
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!-- END whiteboard-tools -->
                        
                        <!-- BEGIN whiteboard-canvas -->
                        <div class="whiteboard-canvas-container" style="border: 2px solid #ddd; border-radius: 8px; overflow: auto;">
                            <canvas id="whiteboardCanvas" width="1800" height="700" style="display: block; cursor: crosshair; background: white;"></canvas>
                        </div>
                        <!-- END whiteboard-canvas -->
                    </div>
                </div>
                <!-- END panel -->
            </div>
        </div>
        <!-- END whiteboard-container -->

        <!-- BEGIN saved-whiteboards -->
        <div class="row mt-4">
            <div class="col-xl-12">
                <div class="panel panel-inverse">
                    <div class="panel-heading">
                        <h4 class="panel-title">Saved Whiteboards</h4>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered align-middle">
                                <thead>
                                    <tr>
                                        <th width="50">Actions</th>
                                        <th>Name</th>
                                        <th width="100">Dimensions</th>
                                        <th width="150">Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($whiteboards)): ?>
                                        <?php foreach ($whiteboards as $whiteboard): ?>
                                            <tr>
                                                <td>
                                                    <button class="btn btn-sm btn-primary me-1" onclick="loadWhiteboard(<?php echo $whiteboard->getId(); ?>)" title="Load">
                                                        <i class="fa fa-folder-open"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-danger" onclick="deleteWhiteboard(<?php echo $whiteboard->getId(); ?>)" title="Delete">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </td>
                                                <td><?php echo htmlspecialchars($whiteboard->getName()); ?></td>
                                                <td><?php echo $whiteboard->getWidth(); ?>x<?php echo $whiteboard->getHeight(); ?></td>
                                                <td><?php echo $whiteboard->getCreated_at(); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="4" class="text-center text-muted">No whiteboards saved yet</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- END saved-whiteboards -->
    </div>
    <!-- END #content -->
</div>
<!-- END #app -->

<!-- Save Modal -->
<div class="modal fade" id="saveModal" tabindex="-1" aria-labelledby="saveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="saveWhiteboardForm" method="POST" action="lwhiteboards">
                <div class="modal-header">
                    <h5 class="modal-title" id="saveModalLabel">Save Whiteboard</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Current whiteboard info when editing -->
                    <div id="currentWhiteboardInfo" class="alert alert-info" style="display: none;">
                        <i class="fa fa-info-circle"></i> You are currently editing: <strong id="currentEditingName"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="whiteboardName" class="form-label">Whiteboard Name:</label>
                        <input type="text" class="form-control" id="whiteboardName" name="whiteboard_name" required>
                        <div class="form-text" id="saveHelpText">Enter a name for your whiteboard.</div>
                    </div>

                    <!-- Quick action buttons when editing existing whiteboard -->
                    <div id="quickActionButtons" class="mb-3" style="display: none;">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-warning" id="overwriteCurrentBtn">
                                <i class="fa fa-save"></i> Overwrite Current Whiteboard
                            </button>
                            <div class="text-center">
                                <small class="text-muted">or enter a different name above to save as new</small>
                            </div>
                        </div>
                    </div>

                    <input type="hidden" id="whiteboardData" name="whiteboard_data">
                    <input type="hidden" id="whiteboardWidth" name="whiteboard_width">
                    <input type="hidden" id="whiteboardHeight" name="whiteboard_height">
                    <input type="hidden" id="overwriteExisting" name="overwrite_existing" value="0">
                    <input type="hidden" id="currentWhiteboardId" name="current_whiteboard_id" value="">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="sub_save_whiteboard" class="btn btn-success" id="saveWhiteboardBtn">Save Whiteboard</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Overwrite Confirmation Modal -->
<div class="modal fade" id="overwriteModal" tabindex="-1" aria-labelledby="overwriteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="overwriteModalLabel">Whiteboard Name Conflict</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">A whiteboard with the name "<strong id="conflictingName"></strong>" already exists. Choose an option:</p>

                <!-- Option 1: Overwrite existing -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">Option 1: Overwrite Existing Whiteboard</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-2">Select which whiteboard to overwrite:</p>
                        <div class="mb-3">
                            <select class="form-select" id="overwriteWhiteboardSelect">
                                <option value="">Select a whiteboard to overwrite...</option>
                            </select>
                        </div>
                        <button type="button" class="btn btn-warning" id="overwriteSelectedBtn" disabled>
                            <i class="fa fa-save"></i> Overwrite Selected Whiteboard
                        </button>
                    </div>
                </div>

                <!-- Option 2: Save with new name -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Option 2: Save with Different Name</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="newWhiteboardName" class="form-label">Enter a new name:</label>
                            <input type="text" class="form-control" id="newWhiteboardName" placeholder="Enter new name...">
                        </div>
                        <button type="button" class="btn btn-primary" id="saveWithNewName">
                            <i class="fa fa-save"></i> Save with New Name
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fa fa-times"></i> Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Load Modal -->
<div class="modal fade" id="loadModal" tabindex="-1" aria-labelledby="loadModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="loadModalLabel">Load Whiteboard</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="whiteboardSearch" placeholder="Search whiteboards..." autocomplete="off">
                </div>
                <div class="list-group" id="whiteboardList">
                    <?php if (!empty($whiteboards)): ?>
                        <?php foreach ($whiteboards as $whiteboard): ?>
                            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center whiteboard-item" data-name="<?php echo htmlspecialchars($whiteboard->getName()); ?>">
                                <div class="flex-grow-1" onclick="loadWhiteboardFromModal(<?php echo $whiteboard->getId(); ?>)" style="cursor: pointer;">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($whiteboard->getName()); ?></h6>
                                        <small><?php echo $whiteboard->getCreated_at(); ?></small>
                                    </div>
                                    <small>Dimensions: <?php echo $whiteboard->getWidth(); ?>x<?php echo $whiteboard->getHeight(); ?></small>
                                </div>
                                <button class="btn btn-sm btn-danger ms-2" onclick="deleteWhiteboardFromModal(<?php echo $whiteboard->getId(); ?>)" title="Delete">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center text-muted p-3" id="noWhiteboardsMessage">No whiteboards available to load</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>
<?php #endregion js ?>

<script>
// Whiteboard functionality
class Whiteboard {

    smoothLine(points, smoothing = 0.2) {
        if (points.length < 2) return points;
        const smoothed = [points[0]];
        for (let i = 1; i < points.length; i++) {
            const prev = smoothed[i - 1];
            const curr = points[i];
            smoothed.push({
                x: prev.x + (curr.x - prev.x) * smoothing,
                y: prev.y + (curr.y - prev.y) * smoothing
            });
        }
        return smoothed;
    }

    startDrawing(e) {
        this.isDrawing = true;
        this.points = [];
        const { x, y } = this.getCoords(e);
        this.points.push({ x, y });
        this.lastX = x;
        this.lastY = y;
    }

    draw(e) {
        if (!this.isDrawing) return;
        const { x, y } = this.getCoords(e);
        this.points.push({ x, y });

        const smoothedPoints = this.smoothLine(this.points, 0.2);
        const ctx = this.ctx;

        ctx.strokeStyle = this.currentColor;
        ctx.lineWidth = this.currentSize;

        ctx.beginPath();
        if (smoothedPoints.length > 1) {
            ctx.moveTo(smoothedPoints[0].x, smoothedPoints[0].y);
            for (let i = 1; i < smoothedPoints.length - 1; i++) {
                const midPoint = {
                    x: (smoothedPoints[i].x + smoothedPoints[i + 1].x) / 2,
                    y: (smoothedPoints[i].y + smoothedPoints[i + 1].y) / 2
                };
                ctx.quadraticCurveTo(smoothedPoints[i].x, smoothedPoints[i].y, midPoint.x, midPoint.y);
            }
            ctx.stroke();
        }
    }

    stopDrawing() {
        this.isDrawing = false;
        this.points = [];
    }

    getCoords(e) {
        const rect = this.canvas.getBoundingClientRect();
        if (e.touches && e.touches[0]) {
            e = e.touches[0];
        }
        return {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
    }

    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.isDrawing = false;
        this.currentTool = 'pen';
        this.currentColor = '#000000';
        this.currentSize = 6;
        this.defaultPenSize = 6;
        this.defaultEraserSize = 50;
        this.lastX = 0;
        this.lastY = 0;

        this.initializeCanvas();
        this.bindEvents();
    }

    initializeCanvas() {
        // Set canvas background to white
        this.ctx.fillStyle = 'white';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Set initial drawing properties
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
        this.ctx.strokeStyle = this.currentColor;
        this.ctx.lineWidth = this.currentSize;
    }

    bindEvents() {
        // Mouse events
        this.canvas.addEventListener('mousedown', this.startDrawing.bind(this));
        this.canvas.addEventListener('mousemove', this.draw.bind(this));
        this.canvas.addEventListener('mouseup', this.stopDrawing.bind(this));
        this.canvas.addEventListener('mouseout', this.stopDrawing.bind(this));

        // Touch events for mobile
        this.canvas.addEventListener('touchstart', this.handleTouch.bind(this));
        this.canvas.addEventListener('touchmove', this.handleTouch.bind(this));
        this.canvas.addEventListener('touchend', this.stopDrawing.bind(this));

        // Tool events
        document.getElementById('penTool').addEventListener('click', () => this.setTool('pen'));
        document.getElementById('eraserTool').addEventListener('click', () => this.setTool('eraser'));
        document.getElementById('clearCanvas').addEventListener('click', () => this.clearCanvas());
        document.getElementById('colorPicker').addEventListener('change', (e) => this.setColor(e.target.value));
        document.getElementById('brushSize').addEventListener('input', (e) => this.setSize(e.target.value));
        document.getElementById('downloadCanvas').addEventListener('click', () => this.downloadCanvas());

        // Update brush size display
        document.getElementById('brushSize').addEventListener('input', (e) => {
            document.getElementById('brushSizeValue').textContent = e.target.value;
        });

        // Canvas size controls
        document.getElementById('applyCanvasSize').addEventListener('click', () => {
            const width = parseInt(document.getElementById('canvasWidth').value);
            const height = parseInt(document.getElementById('canvasHeight').value);
            this.resizeCanvas(width, height);
        });
        document.querySelectorAll('.preset-size').forEach(button => {
            button.addEventListener('click', (e) => {
                const width = parseInt(e.target.dataset.width);
                const height = parseInt(e.target.dataset.height);
                this.setCanvasDimensionsUI(width, height); // Updates UI and calls resize
            });
        });

        // Color palette events
        document.querySelectorAll('.color-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                const color = e.target.dataset.color;
                this.setColor(color);
                document.getElementById('colorPicker').value = color;

                // Update active color button
                document.querySelectorAll('.color-btn').forEach(btn => btn.classList.remove('active'));
                e.target.classList.add('active');
            });
        });
    }

    getMousePos(e) {
        const rect = this.canvas.getBoundingClientRect();
        return {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
    }

    getTouchPos(e) {
        const rect = this.canvas.getBoundingClientRect();
        return {
            x: e.touches[0].clientX - rect.left,
            y: e.touches[0].clientY - rect.top
        };
    }

    startDrawing(e) {
        this.isDrawing = true;
        const pos = this.getMousePos(e);
        this.lastX = pos.x;
        this.lastY = pos.y;
    }

    draw(e) {
        if (!this.isDrawing) return;

        const pos = this.getMousePos(e);

        this.ctx.beginPath();
        this.ctx.moveTo(this.lastX, this.lastY);
        this.ctx.lineTo(pos.x, pos.y);

        if (this.currentTool === 'eraser') {
            this.ctx.globalCompositeOperation = 'destination-out';
        } else {
            this.ctx.globalCompositeOperation = 'source-over';
            this.ctx.strokeStyle = this.currentColor;
        }

        this.ctx.lineWidth = this.currentSize;
        this.ctx.stroke();

        this.lastX = pos.x;
        this.lastY = pos.y;
    }

    stopDrawing() {
        this.isDrawing = false;
    }

    handleTouch(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 'mousemove', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        this.canvas.dispatchEvent(mouseEvent);
    }

    setTool(tool) {
        this.currentTool = tool;

        // Update UI
        document.querySelectorAll('.whiteboard-tools .btn').forEach(btn => btn.classList.remove('active'));
        document.getElementById(tool + 'Tool').classList.add('active');

        // Automatically adjust brush size based on tool
        if (tool === 'eraser') {
            this.setSize(this.defaultEraserSize);
            document.getElementById('brushSize').value = this.defaultEraserSize;
            document.getElementById('brushSizeValue').textContent = this.defaultEraserSize;
            this.canvas.className = 'cursor-grab';
        } else {
            this.setSize(this.defaultPenSize);
            document.getElementById('brushSize').value = this.defaultPenSize;
            document.getElementById('brushSizeValue').textContent = this.defaultPenSize;
            this.canvas.className = 'cursor-crosshair';
        }
    }

    setColor(color) {
        this.currentColor = color;
        if (this.currentTool === 'pen') {
            this.ctx.strokeStyle = color;
        }

        // Update color palette UI
        document.querySelectorAll('.color-btn').forEach(btn => {
            if (btn.dataset.color === color) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
    }

    setSize(size) {
        this.currentSize = size;
        this.ctx.lineWidth = size;
    }

    clearCanvas() {
        if (confirm('Are you sure you want to clear the canvas?')) {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.initializeCanvas();
        }
    }

    getCanvasData() {
        return this.canvas.toDataURL();
    }

    loadCanvasData(dataURL) {
        const img = new Image();
        img.onload = () => {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.drawImage(img, 0, 0);
        };
        img.src = dataURL;
    }

    downloadCanvas() {
        const link = document.createElement('a');
        link.download = 'whiteboard_' + new Date().getTime() + '.png';
        link.href = this.canvas.toDataURL();
        link.click();
    }

    setCanvasDimensionsUI(width, height) {
        document.getElementById('canvasWidth').value = width;
        document.getElementById('canvasHeight').value = height;
        this.resizeCanvas(width, height);
    }

    resizeCanvas(newWidth, newHeight) {
        if (isNaN(newWidth) || isNaN(newHeight) || newWidth <= 0 || newHeight <= 0) {
            alert('Canvas dimensions must be positive numbers.');
            document.getElementById('canvasWidth').value = this.canvas.width;
            document.getElementById('canvasHeight').value = this.canvas.height;
            return;
        }
        const minDim = 100;
        const maxDim = 5000; // Adjusted max to a more reasonable whiteboard size
        if (newWidth < minDim || newHeight < minDim || newWidth > maxDim || newHeight > maxDim) {
            alert(`Canvas dimensions must be between ${minDim}x${minDim} and ${maxDim}x${maxDim} pixels.`);
            document.getElementById('canvasWidth').value = this.canvas.width;
            document.getElementById('canvasHeight').value = this.canvas.height;
            return;
        }

        const currentData = this.canvas.toDataURL();

        this.canvas.width = newWidth;
        this.canvas.height = newHeight;
        this.initializeCanvas(); // Clears, sets background and drawing properties

        const img = new Image();
        img.onload = () => { this.ctx.drawImage(img, 0, 0); };
        img.src = currentData;
    }
}

// Initialize whiteboard when page loads
let whiteboard;
document.addEventListener('DOMContentLoaded', function() {
    try {
        whiteboard = new Whiteboard('whiteboardCanvas');

        // Set initial tool and size
        document.getElementById('penTool').classList.add('active');
        document.getElementById('brushSize').value = 5;
        document.getElementById('brushSizeValue').textContent = '5';

        console.log('Whiteboard initialized successfully');
    } catch (error) {
        console.error('Error initializing whiteboard:', error);
        alert('Error initializing whiteboard. Please refresh the page.');
    }
});

// Global variables to preserve canvas data during save process
let savedCanvasData = null;
let savedCanvasWidth = null;
let savedCanvasHeight = null;
let originalWhiteboardName = null;

// Global variables to track current whiteboard state
let currentWhiteboardId = null;
let currentWhiteboardName = null;
let isEditingExistingWhiteboard = false;

// Save whiteboard function with improved overwrite handling
document.getElementById('saveWhiteboardForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Preserve canvas data at the start of save process
    savedCanvasData = whiteboard.getCanvasData();
    savedCanvasWidth = document.getElementById('whiteboardCanvas').width;
    savedCanvasHeight = document.getElementById('whiteboardCanvas').height;
    originalWhiteboardName = document.getElementById('whiteboardName').value;

    performSave(originalWhiteboardName, false);
});

function performSave(whiteboardName, isOverwrite = false, overwriteId = null) {
    // Prepare form data
    const formData = new FormData();
    formData.append('sub_save_whiteboard', '1');
    formData.append('whiteboard_name', whiteboardName);
    formData.append('whiteboard_data', savedCanvasData);
    formData.append('whiteboard_width', savedCanvasWidth);
    formData.append('whiteboard_height', savedCanvasHeight);
    formData.append('overwrite_existing', isOverwrite ? '1' : '0');
    if (overwriteId) {
        formData.append('overwrite_id', overwriteId);
    }

    // Send AJAX request
    fetch('lwhiteboards', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        // Check if response is JSON or redirect
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return response.json();
        } else {
            // Likely a redirect (success case)
            window.location.reload();
            return null;
        }
    })
    .then(data => {
        if (data === null) return; // Already handled redirect

        if (data.success === false && data.name_exists) {
            // Show improved overwrite modal with whiteboard list
            showOverwriteModal(whiteboardName);
        } else if (data.success === false) {
            alert('Error: ' + (data.message || 'Unknown error occurred'));
        } else {
            // Success
            if (isOverwrite && overwriteId) {
                // Successful overwrite: show message, maintain state
                showSuccessNotification(`Whiteboard "${whiteboardName}" overwritten successfully!`);

                // Use the data returned from server if available
                const actualId = data.whiteboard_id || overwriteId;
                const actualName = data.whiteboard_name || whiteboardName;

                setCurrentWhiteboard(actualId, actualName); // This updates UI indicators and state

                // Close any open modals
                closeAllModals();
            } else {
                // Successful new save or other success scenario that might still need reload
                window.location.reload();
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving. Please try again.');
    });
}

function showOverwriteModal(conflictingName) {
    // Set the conflicting name in the modal
    document.getElementById('conflictingName').textContent = conflictingName;

    // Clear and reset form elements
    document.getElementById('newWhiteboardName').value = '';
    document.getElementById('overwriteWhiteboardSelect').value = '';
    document.getElementById('overwriteSelectedBtn').disabled = true;

    // Populate the overwrite dropdown with existing whiteboards
    populateOverwriteDropdown();

    // Show the modal
    const overwriteModal = new bootstrap.Modal(document.getElementById('overwriteModal'));
    const saveModal = bootstrap.Modal.getInstance(document.getElementById('saveModal'));
    if (saveModal) saveModal.hide();
    overwriteModal.show();
}

function populateOverwriteDropdown() {
    const select = document.getElementById('overwriteWhiteboardSelect');
    select.innerHTML = '<option value="">Select a whiteboard to overwrite...</option>';

    // Get all whiteboard items from the page
    const whiteboardItems = document.querySelectorAll('.whiteboard-item');
    whiteboardItems.forEach(item => {
        const name = item.dataset.name;
        const id = item.querySelector('[onclick*="loadWhiteboardFromModal"]')?.getAttribute('onclick')?.match(/\d+/)?.[0];
        if (name && id) {
            const option = document.createElement('option');
            option.value = id;
            option.textContent = name;
            select.appendChild(option);
        }
    });

    // Also get from the main table if available
    const tableRows = document.querySelectorAll('table tbody tr');
    tableRows.forEach(row => {
        const nameCell = row.cells[1];
        const loadButton = row.querySelector('[onclick*="loadWhiteboard"]');
        if (nameCell && loadButton) {
            const name = nameCell.textContent.trim();
            const id = loadButton.getAttribute('onclick')?.match(/\d+/)?.[0];
            if (name && id) {
                // Check if already added
                const existingOption = select.querySelector(`option[value="${id}"]`);
                if (!existingOption) {
                    const option = document.createElement('option');
                    option.value = id;
                    option.textContent = name;
                    select.appendChild(option);
                }
            }
        }
    });
}

// Load whiteboard functions
function loadWhiteboard(id) {
    fetch('lwhiteboards', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'sub_load_whiteboard=1&whiteboard_id=' + id
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const newWidth = data.data.width;
            const newHeight = data.data.height;

            document.getElementById('canvasWidth').value = newWidth;
            document.getElementById('canvasHeight').value = newHeight;
            whiteboard.canvas.width = newWidth;
            whiteboard.canvas.height = newHeight;
            whiteboard.initializeCanvas(); // Set background for new size
            whiteboard.loadCanvasData(data.data.image_data);

            // Update current whiteboard tracking
            setCurrentWhiteboard(data.data.id, data.data.name);
        } else {
            alert('Error loading whiteboard: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error loading whiteboard');
    });
}

function loadWhiteboardFromModal(id) {
    loadWhiteboard(id);
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('loadModal'));
    modal.hide();
}

// Function to set current whiteboard tracking
function setCurrentWhiteboard(id, name) {
    currentWhiteboardId = id;
    currentWhiteboardName = name;
    isEditingExistingWhiteboard = true;

    // Update UI indicators
    updateCurrentWhiteboardIndicators();
}

// Function to clear current whiteboard (for new whiteboard)
function clearCurrentWhiteboard() {
    currentWhiteboardId = null;
    currentWhiteboardName = null;
    isEditingExistingWhiteboard = false;

    // Clear the canvas
    if (whiteboard) {
        whiteboard.clearCanvas();
    }

    // Update UI indicators
    updateCurrentWhiteboardIndicators();
}

// Function to update UI indicators
function updateCurrentWhiteboardIndicators() {
    const indicator = document.getElementById('currentWhiteboardIndicator');
    const nameElement = document.getElementById('currentWhiteboardName');

    if (isEditingExistingWhiteboard && currentWhiteboardName) {
        nameElement.textContent = currentWhiteboardName;
        indicator.style.display = 'block';
    } else {
        indicator.style.display = 'none';
    }
}

// Function to show success notification
function showSuccessNotification(message) {
    // Create a temporary success alert
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fa fa-check-circle"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(alertDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Function to close all open modals
function closeAllModals() {
    // Close save modal if open
    const saveModal = bootstrap.Modal.getInstance(document.getElementById('saveModal'));
    if (saveModal) {
        saveModal.hide();
    }

    // Close overwrite modal if open
    const overwriteModal = bootstrap.Modal.getInstance(document.getElementById('overwriteModal'));
    if (overwriteModal) {
        overwriteModal.hide();
    }

    // Close load modal if open
    const loadModal = bootstrap.Modal.getInstance(document.getElementById('loadModal'));
    if (loadModal) {
        loadModal.hide();
    }
}

// Function to update save modal based on current state
function updateSaveModal() {
    const modalTitle = document.getElementById('saveModalLabel');
    const currentInfo = document.getElementById('currentWhiteboardInfo');
    const currentEditingName = document.getElementById('currentEditingName');
    const nameInput = document.getElementById('whiteboardName');
    const helpText = document.getElementById('saveHelpText');
    const quickActions = document.getElementById('quickActionButtons');
    const currentIdInput = document.getElementById('currentWhiteboardId');

    if (isEditingExistingWhiteboard && currentWhiteboardName) {
        // Editing existing whiteboard
        modalTitle.textContent = 'Save Whiteboard Changes';
        currentInfo.style.display = 'block';
        currentEditingName.textContent = currentWhiteboardName;
        nameInput.value = currentWhiteboardName;
        helpText.textContent = 'You can overwrite the current whiteboard or save with a different name.';
        quickActions.style.display = 'block';
        currentIdInput.value = currentWhiteboardId;
    } else {
        // Creating new whiteboard
        modalTitle.textContent = 'Save New Whiteboard';
        currentInfo.style.display = 'none';
        nameInput.value = '';
        helpText.textContent = 'Enter a name for your new whiteboard.';
        quickActions.style.display = 'none';
        currentIdInput.value = '';
    }
}

function deleteWhiteboard(id) {
    if (confirm('Are you sure you want to delete this whiteboard?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'lwhiteboards';

        const input1 = document.createElement('input');
        input1.type = 'hidden';
        input1.name = 'sub_delete_whiteboard';
        input1.value = '1';

        const input2 = document.createElement('input');
        input2.type = 'hidden';
        input2.name = 'whiteboard_id';
        input2.value = id;

        form.appendChild(input1);
        form.appendChild(input2);
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteWhiteboardFromModal(id) {
    if (confirm('Are you sure you want to delete this whiteboard?')) {
        deleteWhiteboard(id);
    }
}

// Overwrite modal functionality
document.addEventListener('DOMContentLoaded', function() {
    // Enable/disable overwrite button based on selection
    document.getElementById('overwriteWhiteboardSelect').addEventListener('change', function() {
        const overwriteBtn = document.getElementById('overwriteSelectedBtn');
        overwriteBtn.disabled = !this.value;
    });

    // Overwrite selected whiteboard button
    document.getElementById('overwriteSelectedBtn').addEventListener('click', function() {
        const selectedId = document.getElementById('overwriteWhiteboardSelect').value;
        const selectedName = document.getElementById('overwriteWhiteboardSelect').selectedOptions[0]?.textContent;

        if (selectedId && selectedName) {
            if (confirm(`Are you sure you want to overwrite "${selectedName}"? This action cannot be undone.`)) {
                const overwriteModal = bootstrap.Modal.getInstance(document.getElementById('overwriteModal'));
                overwriteModal.hide();

                // Perform overwrite save
                performSave(selectedName, true, selectedId);
            }
        } else {
            alert('Please select a whiteboard to overwrite.');
        }
    });

    // Save with new name button
    document.getElementById('saveWithNewName').addEventListener('click', function() {
        const newName = document.getElementById('newWhiteboardName').value.trim();
        if (newName) {
            const overwriteModal = bootstrap.Modal.getInstance(document.getElementById('overwriteModal'));
            overwriteModal.hide();

            // Perform save with new name
            performSave(newName, false);
        } else {
            alert('Please enter a new name for the whiteboard.');
        }
    });

    // Search functionality
    const searchInput = document.getElementById('whiteboardSearch');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const whiteboardItems = document.querySelectorAll('.whiteboard-item');
            let visibleCount = 0;

            whiteboardItems.forEach(item => {
                const name = item.dataset.name.toLowerCase();
                if (name.includes(searchTerm)) {
                    item.style.display = '';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });

            // Show/hide no results message
            const noResultsMsg = document.getElementById('noWhiteboardsMessage');
            if (noResultsMsg) {
                if (visibleCount === 0 && whiteboardItems.length > 0) {
                    noResultsMsg.textContent = 'No whiteboards match your search.';
                    noResultsMsg.style.display = '';
                } else if (visibleCount === 0 && whiteboardItems.length === 0) {
                    noResultsMsg.textContent = 'No whiteboards available to load.';
                    noResultsMsg.style.display = '';
                } else {
                    noResultsMsg.style.display = 'none';
                }
            }
        });
    }

    // Update save modal when it's shown
    document.getElementById('saveModal').addEventListener('show.bs.modal', function() {
        updateSaveModal();
    });

    // Reset save modal when closed to preserve canvas content
    document.getElementById('saveModal').addEventListener('hidden.bs.modal', function() {
        // Clear the form but preserve canvas content
        document.getElementById('whiteboardName').value = '';
        document.getElementById('overwriteExisting').value = '0';
        document.getElementById('currentWhiteboardId').value = '';
    });

    // Handle overwrite current whiteboard button
    document.getElementById('overwriteCurrentBtn').addEventListener('click', function() {
        if (currentWhiteboardId && currentWhiteboardName) {
            if (confirm(`Are you sure you want to overwrite "${currentWhiteboardName}"? This action cannot be undone.`)) {
                // Close the save modal
                const saveModal = bootstrap.Modal.getInstance(document.getElementById('saveModal'));
                saveModal.hide();

                // Perform overwrite save with current whiteboard data
                savedCanvasData = whiteboard.getCanvasData();
                savedCanvasWidth = document.getElementById('whiteboardCanvas').width;
                savedCanvasHeight = document.getElementById('whiteboardCanvas').height;

                performSave(currentWhiteboardName, true, currentWhiteboardId);
            }
        } else {
            alert('No current whiteboard to overwrite.');
        }
    });

    // Reset overwrite modal when closed
    document.getElementById('overwriteModal').addEventListener('hidden.bs.modal', function() {
        // Clear form elements
        document.getElementById('newWhiteboardName').value = '';
        document.getElementById('overwriteWhiteboardSelect').value = '';
        document.getElementById('overwriteSelectedBtn').disabled = true;

        // Only show save modal again if user canceled (not if overwrite was successful)
        // We can detect this by checking if the modal was closed due to successful overwrite
        setTimeout(() => {
            // Check if no success notification is currently showing
            const successAlert = document.querySelector('.alert-success');
            if (!successAlert) {
                const saveModal = new bootstrap.Modal(document.getElementById('saveModal'));
                saveModal.show();
            }
        }, 100);
    });
});
</script>
