<div class="dropdown-menu media-list dropdown-menu-end">
						<div class="dropdown-header">@@if(context.theme != 'google'){NOTIFICATIONS}@@if(context.theme == 'google'){Notifications} (5)</div>
						<a href="javascript:;" class="dropdown-item media">
							<div class="media-left">
								<i class="fa fa-bug media-object@@if(context.theme != 'material' && context.theme != 'apple' && context.theme != 'google' && context.theme != 'facebook'){ bg-gray-500}@@if(context.theme == 'material'){ bg-silver-600}@@if(context.theme == 'apple' || context.theme == 'google' || context.theme == 'facebook'){ bg-gray-400}"></i>
							</div>
							<div class="media-body">
								<h6 class="media-heading">Server Error Reports <i class="fa fa-exclamation-circle text-danger"></i></h6>
								<div class="text-muted@@if(context.theme != 'material' && context.theme != 'google'){ fs-10px}@@if(context.theme == 'material'){ fs-11px}@@if(context.theme == 'google'){ fs-12px}">3 minutes ago</div>
							</div>
						</a>
						<a href="javascript:;" class="dropdown-item media">
							<div class="media-left">
								<img src="../assets/img/user/user-1.jpg" class="media-object" alt="" />
								<i class="fab fa-facebook-messenger text-blue media-object-icon"></i>
							</div>
							<div class="media-body">
								<h6 class="media-heading">John Smith</h6>
								<p>Quisque pulvinar tellus sit amet sem scelerisque tincidunt.</p>
								<div class="text-muted@@if(context.theme != 'material' && context.theme != 'google'){ fs-10px}@@if(context.theme == 'material'){ fs-11px}@@if(context.theme == 'google'){ fs-12px}">25 minutes ago</div>
							</div>
						</a>
						<a href="javascript:;" class="dropdown-item media">
							<div class="media-left">
								<img src="../assets/img/user/user-2.jpg" class="media-object" alt="" />
								<i class="fab fa-facebook-messenger text-blue media-object-icon"></i>
							</div>
							<div class="media-body">
								<h6 class="media-heading">Olivia</h6>
								<p>Quisque pulvinar tellus sit amet sem scelerisque tincidunt.</p>
								<div class="text-muted@@if(context.theme != 'material' && context.theme != 'google'){ fs-10px}@@if(context.theme == 'material'){ fs-11px}@@if(context.theme == 'google'){ fs-12px}">35 minutes ago</div>
							</div>
						</a>
						<a href="javascript:;" class="dropdown-item media">
							<div class="media-left">
								<i class="fa fa-plus media-object@@if(context.theme != 'material' && context.theme != 'apple' && context.theme != 'google' && context.theme != 'facebook'){ bg-gray-500}@@if(context.theme == 'material'){ bg-silver-600}@@if(context.theme == 'apple' || context.theme == 'google' || context.theme == 'facebook'){ bg-gray-400}"></i>
							</div>
							<div class="media-body">
								<h6 class="media-heading"> New User Registered</h6>
								<div class="text-muted@@if(context.theme != 'material' && context.theme != 'google'){ fs-10px}@@if(context.theme == 'material'){ fs-11px}@@if(context.theme == 'google'){ fs-12px}">1 hour ago</div>
							</div>
						</a>
						<a href="javascript:;" class="dropdown-item media">
							<div class="media-left">
								<i class="fa fa-envelope media-object@@if(context.theme != 'material' && context.theme != 'apple' && context.theme != 'google' && context.theme != 'facebook'){ bg-gray-500}@@if(context.theme == 'material'){ bg-silver-600}@@if(context.theme == 'apple' || context.theme == 'google' || context.theme == 'facebook'){ bg-gray-400}"></i>
								<i class="fab fa-google text-warning media-object-icon fs-14px"></i>
							</div>
							<div class="media-body">
								<h6 class="media-heading"> New Email From John</h6>
								<div class="text-muted@@if(context.theme != 'material' && context.theme != 'google'){ fs-10px}@@if(context.theme == 'material'){ fs-11px}@@if(context.theme == 'google'){ fs-12px}">2 hour ago</div>
							</div>
						</a>
						<div class="dropdown-footer text-center">
							<a href="javascript:;" class="text-decoration-none">View more</a>
						</div>
					</div>