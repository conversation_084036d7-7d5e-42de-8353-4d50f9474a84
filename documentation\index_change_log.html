<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<head lang="en">
	<meta http-equiv="content-type" content="text/html;charset=utf-8">
	<title> Documentation - Color Admin</title>
	<!-- Bootstrap styles -->
	<link href="assets/bootstrap/css/bootstrap.css" rel="stylesheet">
	<link href="assets/bootstrap/css/bootstrap-responsive.css" rel="stylesheet">
	<link href="assets/bootstrap/css/docs.css" rel="stylesheet">
	<link href="assets/bootstrap/js/google-code-prettify/prettify.css" rel="stylesheet">

	<!-- Le HTML5 shim, for IE6-8 support of HTML5 elements -->
	<!--[if lt IE 9]>
		<script src="assets/js/html5shiv.js"></script>
	<![endif]-->
</head>
<body data-spy="scroll" data-target=".bs-docs-sidebar">
	<div class="navbar navbar-inverse navbar-page">
		<div class="navbar-inner">
			<div class="container">
				<button type="button" class="btn btn-navbar collapsed" data-toggle="collapse" data-target=".nav-collapse">
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>
				<a class="brand" href="#">Admin Template</a>
				<div class="nav-collapse collapse">
					<ul class="nav">
						<li class="">
							<a href="index.html">Design Template</a>
						</li>
						<li class="">
							<a href="index_ajax.html">Ajax Version</a>
						</li>
						<li class="">
							<a href="index_angular_1x.html">Angular 1.x</a>
						</li>
						<li class="">
							<a href="index_angular_13.html">Angular 13.0</a>
						</li>
						<li class="">
							<a href="index_laravel.html">Laravel Version</a>
						</li>
						<li class="">
							<a href="index_vue.html">Vue Version</a>
						</li>
						<li class="">
							<a href="index_react.html">React Version</a>
						</li>
						<li class="">
							<a href="index_asp.html">ASP.NET</a>
						</li>
						<li class="active">
							<a href="index_change_log.html">Change Log</a>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
	<header class="jumbotron subhead" id="overview">
		<div class="container">
			<h1 class="text-center">Color Admin</h1>
			<p class="lead text-center">&ldquo;Change Log&rdquo; Documentation by &ldquo;Sean Ngu&rdquo; v5.1.4</p>
		</div>
		<div class="jumbotron-cover"></div>
	</header>
	<div class="container">
		<div class="row">
			<div class="span12">
				<div class="well with-cover">
					<div class="well-cover" style="background-image: url(assets/images/default.jpg"></div>
					<p>
						<strong>
							Last Updated: 13/February/2022<br>
							By: Sean Ngu<br>
							Email: <a href="mailto:<EMAIL>"><EMAIL></a>
						</strong>
					</p>
					<p>
						Thank you for purchasing my theme. If you have any questions that are beyond the scope of this help file,
						please feel free to email your question to my email <a href="mailTo:<EMAIL>"><EMAIL></a>. Thanks so much!
					</p>
			
				</div>
			</div><!-- end span12 -->
		</div><!-- end row -->
		<div class="row">
			<div class="span3 bs-docs-sidebar">
				<ul class="nav nav-list bs-docs-sidenav affix-top">
					<li><a href="#change-log" style="border-radius: 4px"><i class="icon-chevron-right"></i>Change Log</a></li>
				</ul>
			</div>
			<div class="span9">
				
				<div class="row-fluid">
					<div class="span12">
						<div class="page-header">
							<h3 id="change-log"><strong>A) Change Log</strong> - <a href="#top">top</a></h3>
						</div>
						<h5>Version 5.1.4 - 13 February 2022</h5>
						<ul>
							<li>NEW: Documentation steps for switching design from default theme to others</li>
							<li>UPDATE: FontAwesome v6.0</li>
							<li>UPDATE: Bootstrap Icons v1.8.1</li>
							<li>UPDATE: NPM package</li>
							<li>UPDATE: Angular JS 13 Package</li>
							<li>UPDATE: ReactJS Package</li>
							<li>UPDATE: VueJs Package</li>
							<li>UPDATE: Laravel Package</li>
							<li>UPDATE: ASP Package</li>
							<li>FIX: Dark mode select dropdown for Firefox</li>
							<li>FIX: ReactJS installation issue</li>
							<li>FIX: VueJS installation issue</li>
							<li>FIX: AngularJS installation issue</li>
							<li>FIX: Helper background and color !important issue</li>
						</ul>
						<h5>Version 5.1.3 - 03 December 2021</h5>
						<ul>
							<li>UPDATE: gulp-sass to 5.0.0</li>
							<li>UPDATE: support for nodejs v16+</li>
							<li>UPDATE: Angular JS 13 Package</li>
							<li>UPDATE: ReactJS Package</li>
							<li>UPDATE: VueJs Package</li>
							<li>UPDATE: Laravel Package</li>
							<li>UPDATE: ASP Package</li>
						</ul>
						<h5>Version 5.1.2 - 28 November 2021</h5>
						<ul>
							<li>FIX: React installation</li>
							<li>ENHANCE: Added gulp-autoprefixer for css prefix</li>
							<li>DROP: Mixin (radius, flex, box-shadow) for browser compatible</li>
						</ul>
						<h5>Version 5.1.1 - 24 November 2021</h5>
						<ul>
							<li>FIX: Bootstrap Form Switch / Range Support for Safari</li>
						</ul>
						<h5>Version 5.1.0 - 23 November 2021</h5>
						<ul>
							<li>NEW: Dark Mode - Default / Apple / Google / Facebook / Material / Transparent Version</li>
							<li>NEW: Product Page</li>
							<li>NEW: Order Page</li>
							<li>NEW: Global javascript css variable (jQuery / React / Vue / Asp / Angular / Laravel)</li>
							<li>NEW: CSS Variable Integration (Light / Dark)</li>
							<li>NEW: Bootstrap SCSS Variable Integration (Full)</li>
							<li>NEW: Theme Panel for Vue / React / Asp / Angular Version</li>
							<li>ENHANCE: Transparent Version</li>
							<li>ENHANCE: Login v1 Page</li>
							<li>ENHANCE: 30% lighter CSS</li>
							<li>ENHANCE: Apexchart UI</li>
							<li>ENHANCE: React Select UI</li>
							<li>ENHANCE: React Masonry Card Support</li>
							<li>UPDATE: Bootstrap 5.1.3</li>
							<li>UPDATE: FontAwesome v6 Beta</li>
							<li>UPDATE: All npm packages (jQuery / React / Vue / Asp / Angular / Laravel)</li>
							<li>UPDATE: Asp Nuget Package to 5.0.11</li>
							<li>UPDATE: Laravel 8.69.0</li>
							<li>UPDATE: Angular 13</li>
							<li>DROP: theme file and replaced with theme-* css class</li>
							<li>DROP: Bootstrap Calendar and replaced with jQuery simple calendar</li>
							<li>DROP: widget .inverse-mode class and replaced with .dark-mode class</li>
							<li>DROP: ng2-nvd3 for Angular 13</li>
							<li>FIX: Gulp deprecated plugins source url</li>
							<li>FIX: Angular installation issue</li>
							<li>FIX: React installation issue</li>
						</ul>
						<h5>Version 5.0.0 - 27 June 2021</h5>
						<ul>
							<li>NEW: Frontend Corporate Design</li>
							<li>NEW: All Frontend RTL Support</li>
							<li>NEW: ASP.NET Identity (30+ pages)</li>
							<li>NEW: Floating Forms</li>
							<li>NEW: Offcanvas & Toasts</li>
							<li>NEW: Vendor CSS / JS File (easy to switch between cdn / local files integration)</li>
							<li>NEW: Form plugins</li>
							<li>NEW: Form elements</li>
							<li>NEW: Rewrite the global app component class (check before you upgrade from older version)</li>
							<li>NEW: Rewrite the global app.min.js</li>
							<li>NEW: Rewrite all version css class based on Bootstrap 5</li>
							<li>NEW: Perfect Scrollbar</li>
							<li>NEW: Sidebar Mobile Backdrop</li>
							<li>UPGRADE: Bootstrap 5.0.2</li>
							<li>UPGRADE: Angular 11</li>
							<li>UPGRADE: Asp.net Core 5.0</li>
							<li>UPGRADE: Laravel Package</li>
							<li>UPGRADE: Vue.js Package</li>
							<li>UPGRADE: React.js Package</li>
							<li>ENHANCE: Ajax Version</li>
							<li>ENHANCE: Timeline Page</li>
							<li>ENHANCE: Wizard Page</li>
							<li>ENHANCE: Login V1 Page</li>
							<li>ENHANCE: CKEditor / Bootstrap-Wysihtml5</li>
							<li>MIGRATE: Predefined CSS (left & right) changed to (start & end)</li>
							<li>FIX: Boxed Layout with Mixed Menu</li>
							<li>DROP: slimscroll</li>
							<li>DROP: label (replaced with badge)</li>
							<li>DROP: bootstrap-select</li>
							<li>DROP: bootstrap-show-password</li>
							<li>DROP: pwstrength-bootstrap</li>
							<li>DROP: datatable-autofill</li>
						</ul>
						<h5>Version 4.7.0 - 25 October 2020</h5>
						<ul>
							<li>NEW: Asp.net Core 3.1 MVC Version</li>
							<li>NEW: POS - Customer Order Design</li>
							<li>NEW: POS - Kitchen Order Design</li>
							<li>NEW: POS - Counter Checkout Design</li>
							<li>NEW: POS - Table Booking Design</li>
							<li>NEW: POS - Menu Stock Design</li>
							<li>NEW: Form Wizard Design</li>
							<li>NEW: Vue.js SCSS configuration</li>
							<li>NEW: React.js SCSS configuration</li>
							<li>NEW: Laravel SCSS configuration</li>
							<li>NEW: Angular JS 10 SCSS configuration</li>
							<li>NEW: ngx-datatable for Angular 10</li>
							<li>UPGRADE: Bootstrap 4.5.3</li>
							<li>UPGRADE: FontAwesome 5.15.1</li>
							<li>UPGRADE: Flot Chart 4.x (with new configuration method)</li>
							<li>UPGRADE: Fullcalendar 5.x (with new configuration method)</li>
							<li>UPGRADE: All other js plugins version</li>
							<li>UPGRADE: Laravel 8.x</li>
							<li>UPGRADE: Angular 10.0</li>
							<li>FIX: NPM Package vulnerability</li>
							<li>FIX: Laravel version sidebar route active matching</li>
							<li>FIX: One Page Parallax display issue on smaller device</li>
							<li>FIX: Minor bug fix for admin template</li>
							<li>DROP: Morris Chart</li>
							<li>DROP: jQuery Wizards</li>
							<li>DROP: ng2-table for Angular 10</li>
						</ul>
						<h5>Version 4.6.0 - 12 January 2020</h5>
						<ul>
							<li>NEW: Google Design</li>
							<li>NEW: Scrum Board</li>
							<li>NEW: Cookie Acceptance Page</li>
							<li>NEW: Page with Search Sidebar</li>
							<li>NEW: gulp-file-include support</li>
							<li>NEW: Daterangepicker for Angular / React / Vue JS</li>
							<li>NEW: Sidebar filter function for HTML / Angular / React / Vue JS</li>
							<li>UPGRADE: Bootstrap 4.4.1</li>
							<li>UPGRADE: FontAwesome 5.12.0</li>
							<li>UPGRADE: jQuery 3.4.1</li>
							<li>UPGRADE: Animate.css 3.7.2</li>
							<li>UPGRADE: js-cookie 2.2.1</li>
							<li>UPGRADE: Popper.js 1.16.0</li>
							<li>UPGRADE: HTML NPM Package</li>
							<li>UPGRADE: VueJS NPM Package</li>
							<li>UPGRADE: ReactJS NPM Package</li>
							<li>UPGRADE: Angular 8.0 NPM Package</li>
							<li>UPGRADE: Laravel Composer & NPM Package</li>
							<li>UPGRADE: Frontend Template NPM Package</li>
							<li>FIX: Angular 8 top menu expand / collapse state in desktop mode</li>
							<li>ENHANCE: UXUI for All Plugins</li>
							<li>FIX: Remove unused plugins for Angular 8</li>
							<li>FIX: Laravel documentation page missing folder</li>
						</ul>
						<h5>Version 4.5.0 - 07 September 2019</h5>
						<ul>
							<li>FIX: Angular 8 documentation typo</li>
							<li>FIX: Angular 8 ngx-datatable error</li>
							<li>FIX: app.min.js minified version</li>
						</ul>
						<h5>Version 4.4.0 - 05 August 2019</h5>
						<ul>
							<li>NEW: Dashboard V3</li>
							<li>NEW: Apex Chart</li>
							<li>NEW: Admin Template Gulp with SCSS Support</li>
							<li>NEW: Frontend Template Gulp with SCSS Support</li>
							<li>NEW: Angular 8.0 - Minified Sidebar Sub Menu Support</li>
							<li>NEW: Angular 8.0 - Perfect Scrollbar Plugins</li>
							<li>NEW: React JS - Minified Sidebar Sub Menu Support</li>
							<li>NEW: React JS - Perfect Scrollbar Plugins</li>
							<li>NEW: Vue JS - Minified Sidebar Sub Menu Support</li>
							<li>NEW: Vue JS - Perfect Scrollbar Plugins</li>
							<li>NEW: Gulp Support</li>
							<li>NEW: SCSS RTL Support</li>
							<li>NEW: SCSS Support for All Admin Template</li>
							<li>NEW: SCSS Support for All Frontend Template</li>
							<li>NEW: package.json Package Management</li>
							<li>UPGRADE: Angular 8.0</li>
							<li>UPGRADE: Bootstrap 4.3.1</li>
							<li>UPGRADE: Larvel 7.x</li>
							<li>UPGRADE: ReactJS Plugins</li>
							<li>UPGRADE: VueJS Plugins</li>
							<li>ENHANCE: Rewrite all css code based on Bootstrap 4 SCSS compilation</li>
							<li>ENHANCE: UXUI for All Plugins</li>
							<li>FIX: Angular 8.0 Plugins Dependencies</li>
							<li>FIX: React JS Plugins Dependencies</li>
							<li>FIX: Vue JS Plugins Dependencies</li>
							<li>DROP: Bootstrap 3 Support</li>
							<li>DROP: LESS Support</li>
						</ul>
						<h5>Version 4.3.0 - 16 February 2019</h5>
						<ul>
							<li>
								NEW: React JS Version
							</li>
							<li>
								NEW: Facebook Design
							</li>
							<li>
								NEW: Startup Page for Facebook Design
							</li>
							<li>
								UPGRADE: Bootstrap 4.2.1
							</li>
							<li>
								UPGRADE: FontAwesome 5.7.0
							</li>
							<li>
								UPGRADE: Angular 7.0
							</li>
							<li>
								FIX: Laravel missing variable
							</li>
							<li>
								FIX: Bootstrap dropdown click
							</li>
						</ul>
						<h5>Version 4.2.0 - 16 September 2018</h5>
						<ul>
							<li>
								NEW: Vue Version
							</li>
							<li>
								NEW: Startup Page for HTML / APPLE / MATERIAL / ANGULAR / ANGULAR 6 / LARAVEL / VUE 
							</li>
							<li>
								NEW: Angular JS 6 Sidebar Scroll Memory
							</li>
							<li>
								NEW: Angular JS 6 Full Version Page Options
							</li>
							<li>
								NEW: Angular JS 6 Panel Component
							</li>
							<li>
								NEW: Angular JS 6 Ngx DataTables
							</li>
							<li>
								UPGRADE: E-Commerce / Blog / Forum / One Page Parallax to Bootstrap 4
							</li>
							<li>
								UPGRADE: Bootstrap v4.1.3
							</li>
							<li>
								UPGRADE: jQuery v3.3.1
							</li>
							<li>
								UPGRADE: FontAwesome v5.3.1
							</li>
							<li>
								UPGRADE: Ionicons v4.0
							</li>
							<li>
								UPGRADE: Summernote v0.8.10
							</li>
							<li>
								UPGRADE: jQuery Smart Wizard v4.3.1
							</li>
							<li>
								UPGRADE: Select2 v4.0.5
							</li>
							<li>
								UPGRADE: Parsley JS v2.8.1
							</li>
							<li>
								UPGRADE: Moment v2.22.2
							</li>
							<li>
								UPGRADE: Lity v2.3.1
							</li>
							<li>
								UPGRADE: Lightbox2 v2.10.0
							</li>
							<li>
								UPGRADE: Jstree v3.3.5
							</li>
							<li>
								UPGRADE: jQuery-mockjax v2.4.0
							</li>
							<li>
								UPGRADE: jQuery Knob 1.2.11
							</li>
							<li>
								UPGRADE: IonRangeSlider v2.2.0
							</li>
							<li>
								UPGRADE: Intro JS v2.9.0
							</li>
							<li>
								UPGRADE: FullCalendar v3.9.0
							</li>
							<li>
								UPGRADE: Bootstrap Eonasdan Datetimepicker v4.17.47
							</li>
							<li>
								UPGRADE: Dropzone v5.3.0
							</li>
							<li>
								UPGRADE: DataTables v1.10.18
							</li>
							<li>
								UPGRADE: DataTables Extension - AutoFill v2.3.0
							</li>
							<li>
								UPGRADE: DataTables Extension - Buttons  v1.5.2
							</li>
							<li>
								UPGRADE: DataTables Extension - ColReorder  v1.5.0
							</li>
							<li>
								UPGRADE: DataTables Extension - FixedColumns  v3.2.5
							</li>
							<li>
								UPGRADE: DataTables Extension - FixedHeader  v3.1.4
							</li>
							<li>
								UPGRADE: DataTables Extension - JSZip  v2.5.0
							</li>
							<li>
								UPGRADE: DataTables Extension - KeyTable  v2.4.0
							</li>
							<li>
								UPGRADE: DataTables Extension - PDFMake  v0.1.36
							</li>
							<li>
								UPGRADE: DataTables Extension - Responsive  v2.2.2
							</li>
							<li>
								UPGRADE: DataTables Extension - RowReorder  v1.2.4
							</li>
							<li>
								UPGRADE: DataTables Extension - Scroller  v1.5.0
							</li>
							<li>
								UPGRADE: DataTables Extension - Select  v1.2.6
							</li>
							<li>
								UPGRADE: NVD3 v1.8.6
							</li>
							<li>
								UPGRADE: D3 v3.5.17
							</li>
							<li>
								UPGRADE: Clipboard JS v2.0.1
							</li>
							<li>
								UPGRADE: Chart JS v2.7.2
							</li>
							<li>
								UPGRADE: WYSIHTML5 v0.3.3
							</li>
							<li>
								UPGRADE: Bootstrap Timepicker v0.5.2
							</li>
							<li>
								UPGRADE: Bootstrap Daterangepicker v3.0.3
							</li>
							<li>
								UPGRADE: Bootstrap Datepicker v1.8.0
							</li>
							<li>
								UPGRADE: Boostrap Color Picker v2.5.2
							</li>
							<li>
								UPGRADE: blueImp Gallery v2.33.0
							</li>
							<li>
								UPGRADE: jQuery File Upload v9.22.0
							</li>
							<li>
								UPGRADE: Bootstrap Select v1.13.1
							</li>
							<li>
								ENHANCE: Remove all mixed space / tabs 
							</li>
							<li>
								FIX: Login / Register V3 Mobile Scrolling Issue
							</li>
							<li>
								FIX: Slimscroll Unable to scroll on page load
							</li>
							<li>
								FIX: Angular JS 6 Minified Sidebar Sub Menu
							</li>
							<li>
								FIX: Boxed Layout with Fixed Minified Sidebar
							</li>
						</ul>
						<h5>Version 4.1.1 - 03 June 2018</h5>
						<ul>
							<li>
								FIX: Angular 6 version
							</li>
							<li>
								FIX: Swal Notification Transparent Version
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Summernote Transparent Version
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Page with Two Sidebar RTL Mode
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Compatibility Two Sidebar with Wide Sidebar
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: IE11 Invalid / valid tooltip styling
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: IE11 Full Height Options
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: IE11 Dashboard v2
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 4.1.0 - 28 May 2018</h5>
						<ul>
							<li>
								NEW: Widgets Page
								<ul>
									<li>Added files: <code>widget.html</code></li>
								</ul>
							</li>
							<li>
								NEW: RTL Support
								<ul>
									<li>Modified files: <code>/assets/css/default/style.css</code></li>
									<li>Modified files: <code>/assets/css/default/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: SCSS Support
								<ul>
									<li>Added directories: <code>/assets/less</code></li>
								</ul>
							</li>
							<li>
								NEW: 6 Color Theme
								<ul>
									<li>Added files: <code>/assets/css/default/theme/aqua.css</code></li>
									<li>Added files: <code>/assets/css/default/theme/pink.css</code></li>
									<li>Added files: <code>/assets/css/default/theme/yellow.css</code></li>
									<li>Added files: <code>/assets/css/default/theme/indigo.css</code></li>
									<li>Added files: <code>/assets/css/default/theme/green.css</code></li>
									<li>Added files: <code>/assets/css/default/theme/lime.css</code></li>
								</ul>
							</li>
							<li>
								NEW: 6 Color Theme for Alert, Button, Label, Badge, Note
								<ul>
									<li>Modified files: <code>ui_general.html</code></li>
									<li>Modified files: <code>ui_buttons.html</code></li>
									<li>Modified files: <code>/assets/css/default/style.css</code></li>
									<li>Modified files: <code>/assets/css/default/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: CSS Form Switcher with 12 Color Themes
								<ul>
									<li>Modified files: <code>form_elements.html</code></li>
									<li>Modified files: <code>/assets/css/default/style.css</code></li>
									<li>Modified files: <code>/assets/css/default/style.min.css</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap 4.1.1
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: FontAwesome 5.0.13
								<ul>
									<li>Updated Directory: <code>/assets/plugins/fontawesome/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Laravel 5.6
								<ul>
									<li>Modified files: <code>composer.json</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Angular 6
								<ul>
									<li>Upgrade from v5 to v6 <a href="https://stackoverflow.com/questions/48970553/want-to-upgrade-project-from-angular-v5-to-angular-v6" target="_blank">command</a></li>
								</ul>
							</li>
							<li>
								FIX: FullCalendar table header alignment issue
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Minified Sidebar for expanded profile menu
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 4.0.0 - 01 March 2018</h5>
						<ul>
							<li>
								NEW: Transparent Design
								<ul>
									<li>Added files: <code>template_transparent</code></li>
								</ul>
							</li>
							<li>
								NEW: Angular Laravel Version
								<ul>
									<li>Added files: <code>template_laravel</code></li>
								</ul>
							</li>
							<li>
								NEW: Angular JS 5 Version
								<ul>
									<li>Added files: <code>template_angularjs5</code></li>
								</ul>
							</li>
							<li>
								NEW: Profile Page
								<ul>
									<li>Updated HTML files: <code>extra_profile.html</code></li>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Email Inbox Page
								<ul>
									<li>Updated HTML files: <code>email_inbox.html</code></li>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Email Detail Page
								<ul>
									<li>Updated HTML files: <code>email_detail.html</code></li>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Email Compose Page
								<ul>
									<li>Updated HTML files: <code>email_compose.html</code></li>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: jQuery Smart Wizard
								<ul>
									<li>Updated HTML files: <code>form_wizards.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/jquery-smart-wizard/</code></li>
								</ul>
							</li>
							<li>
								NEW: Highlight JS
								<ul>
									<li>Added PLUGINS files: <code>/assets/plugins/highlight/</code></li>
								</ul>
							</li>
							<li>
								NEW: Minified Submenu Support for Fixed Sidebar
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
									<li>Modified File: <code>apps.js</code></li>
									<li>Modified File: <code>apps.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Bootstrap 3 & 4 Support
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
									<li>Modified File: <code>apps.js</code></li>
									<li>Modified File: <code>apps.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: LESS Support for Apple & Transparent Design
								<ul>
									<li>Added Files: <code>/assets/less/apple</code></li>
									<li>Added Files: <code>/assets/less/transparent</code></li>
								</ul>
							</li>
							<li>
								ENHANCE: Overall UI Enhancement
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								ENHANCE: Standardize File structure
								<ul>
									<li>Modified Path: <code>/assets</code></li>
								</ul>
							</li>
							<li>
								ENHANCE: Ajax Version
								<ul>
									<li>Modified File: <code>apps.js</code></li>
									<li>Modified File: <code>apps.min.css</code></li>
								</ul>
							</li>
							<li>
								UPDATE: jQuery 3.x
								<ul>
									<li>Updated Directory: <code>/assets/plugins/jquery/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap 4.0
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: FontAwesome 5.0 
								<ul>
									<li>Updated Directory: <code>/assets/plugins/font-awesome/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: DataTables 1.10.10
								<ul>
									<li>Updated Directory: <code>/assets/plugins/DataTables/</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 3.0.0 - 23 July 2017</h5>
						<ul>
							<li>
								NEW: Apple Design
								<ul>
									<li>Added files: <code>template_content_apple</code></li>
								</ul>
							</li>
							<li>
								NEW: Angular JS 4
								<ul>
									<li>Added files: <code>template_content_angularjs4</code></li>
								</ul>
							</li>
							<li>
								NEW: Boostrap 4 Support
								<ul>
									<li>Added HTML files: <code>bootstrap_4.html</code></li>
									<li>Added CSS files: <code>/assets/css/style-bs4.css</code></li>
									<li>Added CSS files: <code>/assets/css/style-bs4.min.css</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/bootstrap4/</code></li>
								</ul>
							</li>
							<li>
								NEW: CSS Checkbox & Radiobutton
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: clipboard.js
								<ul>
									<li>Updated HTML files: <code>form_plugins.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/clipboard/</code></li>
								</ul>
							</li>
							<li>
								NEW: Bootstrap SweetAlert.js
								<ul>
									<li>Updated HTML files: <code>ui_modal_notification.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/bootstrap-sweetalert/</code></li>
								</ul>
							</li>
							<li>
								ENHANCE: Angular JS Mobile Sidebar AutoClose
								<ul>
									<li>Modified File: <code>apps.js</code></li>
									<li>Modified File: <code>apps.min.js</code></li>
								</ul>
							</li>
							<li>
								ENHANCE: Angular JS 4 Mobile Sidebar AutoClose
								<ul>
									<li>Modified File: <code>apps.js</code></li>
									<li>Modified File: <code>apps.min.js</code></li>
								</ul>
							</li>
							<li>
								ENHANCE: Ajax Version Mobile Sidebar AutoClose
								<ul>
									<li>Modified File: <code>apps.js</code></li>
									<li>Modified File: <code>apps.min.js</code></li>
								</ul>
							</li>
							<li>
								ENHANCE: Add listener after panel reposition
								<ul>
									<li>Modified File: <code>apps.js</code></li>
									<li>Modified File: <code>apps.min.js</code></li>
								</ul>
							</li>
							<li>
								ENHANCE: Form Plugins UI Enhancement
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Sidebar Transparent Overflow Issue
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: IE8 Datetimcpicker
								<ul>
									<li>Modified File: <code>form-plugins.demo.js</code></li>
									<li>Modified File: <code>form-plugins.demo.min.js</code></li>
								</ul>
							</li>
							<li>
								FIX: Login v1 Overflow Issue
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 2.2.0 - 04 January 2017</h5>
						<ul>
							<li>
								FIX: Downloaded File Unable to Extract
								<ul>
									<li>Removed File: <code>template_content_angularjs2/node_modules</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 2.1.0 - 30 November 2016</h5>
						<ul>
							<li>
								NEW: Angular JS 2
								<ul>
									<li>Added files: <code>template_content_angularjs2</code></li>
								</ul>
							</li>
							<li>
								NEW: LESS File for Material Design
								<ul>
									<li>Added files: <code>template_content_material/assets/less/</code></li>
								</ul>
							</li>
							<li>
								NEW: Bootstrap Color Palette
								<ul>
									<li>Updated HTML files: <code>form_plugins.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/bootstrap-colorpalette/</code></li>
								</ul>
							</li>
							<li>
								NEW: Bootstrap Social
								<ul>
									<li>Added HTML files: <code>ui_social_buttons.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/bootstrap-social/</code></li>
								</ul>
							</li>
							<li>
								NEW: Intro JS
								<ul>
									<li>Added HTML files: <code>ui_tour.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/introjs/</code></li>
								</ul>
							</li>
							<li>
								NEW: jQuery Simple Color Picker
								<ul>
									<li>Updated HTML files: <code>form_plugins.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/jquery-simplecolorpicker/</code></li>
								</ul>
							</li>
							<li>
								NEW: Bootstrap Show Password
								<ul>
									<li>Updated HTML files: <code>form_plugins.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/bootstrap-show-password/</code></li>
								</ul>
							</li>
							<li>
								NEW: Dropzone
								<ul>
									<li>Added HTML files: <code>form_dropzone.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/dropzone/</code></li>
								</ul>
							</li>
							<li>
								NEW: Summernote
								<ul>
									<li>Added HTML files: <code>form_summernote.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/summernote/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap 3.3.7 
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Font Awesome 4.7.0
								<ul>
									<li>Updated Directory: <code>/assets/plugins/font-awesome/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Colorpicker 2.3.2
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap-colorpicker/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap Combobox 1.1.7
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap-combobox/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap Datepicker 1.6.1
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap-datepicker/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap Daterangepicker 2.1.24
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap-daterangepicker/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap Datetimepicker
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap-datetimepicker/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap Eonasdan Datetimepicker 4.17.37
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap-eonasdan-datetimepicker/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap Tags Input 0.8.0
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap-tagsinput/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap XEditable 1.5.1
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap3-editable/</code></li>
								</ul>
							</li>
							<li>
								DROP: Bootstrap WYSIHTML5 and replaced with WYSIHTML5 Bootstrap3
								<ul>
									<li>New Directory: <code>/assets/plugins/bootstrap-wysihtml5/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Chart JS 2.0.0
								<ul>
									<li>Updated Directory: <code>/assets/plugins/chart-js/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Flag Icon 2.4.0
								<ul>
									<li>Updated Directory: <code>/assets/plugins/flag-icon/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Flot Chart 0.8.3
								<ul>
									<li>Updated Directory: <code>/assets/plugins/flot/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Fullcalendar 2.9.1
								<ul>
									<li>Updated Directory: <code>/assets/plugins/fullcalendar/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Ionicon 2.0.1
								<ul>
									<li>Updated Directory: <code>/assets/plugins/ionicon/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Ion Range Slider 2.1.4
								<ul>
									<li>Updated Directory: <code>/assets/plugins/ionRangeSlider/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: jQuery File Upload
								<ul>
									<li>Updated Directory: <code>/assets/plugins/jquery-file-upload/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: jVector Map 2.0.3
								<ul>
									<li>Updated Directory: <code>/assets/plugins/jquery-jvectormap/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: jQuery Countdown 2.0.2
								<ul>
									<li>Updated Directory: <code>/assets/plugins/jquery.countdown/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: jsTree 3.3.2
								<ul>
									<li>Updated Directory: <code>/assets/plugins/jstree/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: LESS 2.7.1
								<ul>
									<li>Updated Directory: <code>/assets/plugins/less/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Lightbox 2.8.2
								<ul>
									<li>Updated Directory: <code>/assets/plugins/lightbox/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Masked Input 1.4.1
								<ul>
									<li>Updated Directory: <code>/assets/plugins/masked-input/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Morris Chart 0.5.1
								<ul>
									<li>Updated Directory: <code>/assets/plugins/morris/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: NVD3 1.8.4
								<ul>
									<li>Updated Directory: <code>/assets/plugins/nvd3/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Parsley 2.4.4
								<ul>
									<li>Updated Directory: <code>/assets/plugins/parsley/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Simple Line Icons 2.3.2
								<ul>
									<li>Updated Directory: <code>/assets/plugins/simple-line-icons/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Slimscroll 1.3.8
								<ul>
									<li>Updated Directory: <code>/assets/plugins/slimscroll/</code></li>
								</ul>
							</li>
							<li>
								FIX: Material Version Inverse Header
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Table Manage Combination PDF button not working
								<ul>
									<li>Modified File: <code>table_manage_combine.html</code></li>
								</ul>
							</li>
							<li>
								FIX: Material Version icons rendering problem in mobile device
								<ul>
									<li>Modified File: <code>table_manage_combine.html</code></li>
								</ul>
							</li>
							<li>
								FIX: Missing Profile / Invoice / Calendar page styling for LESS 
								<ul>
									<li>Modified File: <code>/assets/less/</code></li>
								</ul>
							</li>
							<li>
								FIX: Login V1 / V2 styling overflow
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
							<li>
								ENHANCEMENT: Minor UI Enhancement
								<ul>
									<li>Modified File: <code>style.css</code></li>
									<li>Modified File: <code>style.min.css</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 2.0.0 - 30 April 2016</h5>
						<ul>
							<li>
								NEW: Material Design Version
								<ul>
									<li>Added files: <code>template_content_material</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap 3.3.6 
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: FontAwesome 4.6.1
								<ul>
									<li>Updated Directory: <code>/assets/plugins/font-awesome/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Angular Js  1.5.5 (stable version)
								<ul>
									<li>Updated Directory: <code>/assets/plugins/angularjs/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Fullcalendar 2.0
								<ul>
									<li>Updated Directory: <code>/assets/plugins/fullcalendar/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: DataTable 1.10.10
								<ul>
									<li>Updated Directory: <code>/assets/plugins/fullcalendar/</code></li>
								</ul>
							</li>
							<li>
								FIX: Angular Version Panel Dragging Issue
								<ul>
									<li>Modified File: <code>/assets/js/apps.js</code></li>
								</ul>
							</li>
							<li>
								FIX: Angular Version Page Height Issue
								<ul>
									<li>Modified File: <code>/assets/css/style.css</code></li>
									<li>Modified File: <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Select 2 Overflow Issue
								<ul>
									<li>Modified File: <code>/assets/css/style.css</code></li>
									<li>Modified File: <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: DataTables Buttons Extension Issue
								<ul>
									<li>Modified File: <code>/assets/plugins/DataTables/</code></li>
								</ul>
							</li>
							<li>
								FIX: Full Height Page Panel Expand Issue
								<ul>
									<li>Modified File: <code>/assets/css/style.css</code></li>
									<li>Modified File: <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Slider & Switchery Options
								<ul>
									<li>Modified File: <code>/assets/js/form-slider-switcher.demo.js</code></li>
									<li>Modified File: <code>/assets/js/form-slider-switcher.demo.min.js</code></li>
								</ul>
							</li>
							<li>
								FIX: Page Loader z-index Issue
								<ul>
									<li>Modified File: <code>/assets/js/form-slider-switcher.demo.js</code></li>
									<li>Modified File: <code>/assets/js/form-slider-switcher.demo.min.js</code></li>
								</ul>
							</li>
							<li>
								FIX: Spinner Margin Issue
								<ul>
									<li>Modified File: <code>/assets/css/style.css</code></li>
									<li>Modified File: <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 1.9.0 - 05 October 2015</h5>
						<ul>
							<li>
								NEW: Angular JS Version
								<ul>
									<li>Added files: <code>template_content_angularjs</code></li>
								</ul>
							</li>
							<li>
								NEW: NVD3 Chart
								<ul>
									<li>Added HTML files: <code>chart_d3.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/nvd3/</code></li>
								</ul>
							</li>
							<li>
								NEW: Page with Top Menu
								<ul>
									<li>Added HTML files: <code>page_with_top_menu.html</code></li>
									<li>Modified CSS files: <code>/assets/css/style.css</code> and <code>/assets/css/style.min.css</code></li>
									<li>Modified JS files: <code>/assets/js/apps.js</code> and <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								NEW: Boxed Layout
								<ul>
									<li>Added HTML files: <code>page_with_boxed_layout.html</code></li>
									<li>Modified CSS files: <code>/assets/css/style.css</code> and <code>/assets/css/style.min.css</code></li>
									<li>Modified JS files: <code>/assets/js/apps.js</code> and <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								NEW: Page with Mixed Menu
								<ul>
									<li>Added HTML files: <code>page_with_mixed_menu.html</code></li>
									<li>Modified CSS files: <code>/assets/css/style.css</code> and <code>/assets/css/style.min.css</code></li>
									<li>Modified JS files: <code>/assets/js/apps.js</code> and <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								NEW: Boxed Layout with Mixed Menu
								<ul>
									<li>Added HTML files: <code>page_boxed_layout_with_mixed_menu.html</code></li>
									<li>Modified CSS files: <code>/assets/css/style.css</code> and <code>/assets/css/style.min.css</code></li>
									<li>Modified JS files: <code>/assets/js/apps.js</code> and <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								NEW: Transparent Sidebar
								<ul>
									<li>Added HTML files: <code>page_with_transparent_sidebar.html</code></li>
									<li>Modified CSS files: <code>/assets/css/style.css</code> and <code>/assets/css/style.min.css</code></li>
									<li>Modified JS files: <code>/assets/js/apps.js</code> and <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								NEW: DataTables Extension - RowReorder
								<ul>
									<li>Added HTML files: <code>table_manage_rowreorder.html</code></li>
									<li>Modified CSS files: <code>/assets/css/style.css</code> and <code>/assets/css/style.min.css</code></li>
									<li>Modified JS files: <code>/assets/js/apps.js</code> and <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								NEW: DataTables Extension - Select
								<ul>
									<li>Added HTML files: <code>table_manage_select.html</code></li>
									<li>Modified CSS files: <code>/assets/css/style.css</code> and <code>/assets/css/style.min.css</code></li>
									<li>Modified JS files: <code>/assets/js/apps.js</code> and <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								UPDATE: DataTables Version - 1.10.9
								<ul>
									<li>Updated Plugins files: <code>/assets/DataTables</code></li>
								</ul>
							</li>
							<li>
								ENHANCED: Color Admin Apps.js
								<ul>
									<li>Modified JS files: <code>/assets/js/apps.js</code> and <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 1.8.0 - 30 June 2015</h5>
						<ul>
							<li>
								NEW: Bootstrap Date Time Picker
								<ul>
									<li>Modified HTML files: <code>form_plugins.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/bootstrap-eonasdan-datetimepicker/</code></li>
								</ul>
							</li>
							<li>
								NEW: Full Color Panel
								<ul>
									<li>Modified HTML files: <code>ui_widget_boxes.html</code></li>
									<li>Modified CSS files: <code>/assets/css/style.css</code></li>
									<li>Modified CSS files: <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Login Page v2 Background in Large Screen (> 1920px)
								<ul>
									<li>Modified CSS files: <code>/assets/css/style.css</code></li>
									<li>Modified CSS files: <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Page Sidebar Expand / Collapse Height Rendering
								<ul>
									<li>Modified JS files: <code>/assets/js/apps.js</code></li>
									<li>Modified JS files: <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								FIX: Page Sidebar Scrollbar Touch Event in Tablet Device
								<ul>
									<li>Modified JS files: <code>/assets/js/apps.js</code></li>
									<li>Modified JS files: <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								FIX: Clearfix Mixin Crashed with Bootstrap Mixin
								<ul>
									<li>Updated File: <code>/assets/less/_mixins.less</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Include Bootstrap LESS 
								<ul>
									<li>Updated Directory: <code>/assets/less/bootstrap/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap 3.3.4 to Bootstrap 3.3.5
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Less 1.7.4 to Less 2.5.1
								<ul>
									<li>Updated Directory: <code>/assets/plugins/less/</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 1.7.0 - 1 May 2015</h5>
						<ul>
							<li>
								NEW: Tree View
								<ul>
									<li>Added HTML files: <code>ui_tree.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/jstree/</code></li>
								</ul>
							</li>
							<li>
								NEW: Language Bar & Icon List
								<ul>
									<li>Added HTML files: <code>ui_language_bar_icon.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/flag-icon/</code></li>
								</ul>
							</li>
							<li>
								NEW: Bootstrap Date Range Picker
								<ul>
									<li>Updated HTML files: <code>form_plugins.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/bootstrap-daterangepicker/</code></li>
								</ul>
							</li>
							<li>
								NEW: Select2
								<ul>
									<li>Updated HTML files: <code>form_plugins.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/select2/</code></li>
								</ul>
							</li>
							<li>
								NEW: Top Navbar Mega Menu
								<ul>
									<li>Added HTML files: <code>page_with_mega_menu.html</code></li>
								</ul>
							</li>
							<li>
								NEW: Login & Register Page v3
								<ul>
									<li>Added HTML files: <code>login_v3.html</code></li>
									<li>Added HTML files: <code>register_v3.html</code></li>
								</ul>
							</li>
							<li>
								NEW: Profile Page
								<ul>
									<li>Added HTML files: <code>extra_profile.html</code></li>
								</ul>
							</li>
							<li>
								NEW: Light Sidebar
								<ul>
									<li>Added HTML files: <code>page_with_light_sidebar.html</code></li>
									<li>Modified CSS files: <code>/assets/css/style.css</code> and <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Wide Sidebar
								<ul>
									<li>Added HTML files: <code>page_with_wide_sidebar.html</code></li>
									<li>Modified CSS files: <code>/assets/css/style.css</code> and <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Mobile View Scrollbar
								<ul>
									<li>Modified JS files: <code>/assets/js/apps.js</code> and <code>/assets/css/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								NEW: Mobile View Minified Sidebar Scrollbar
								<ul>
									<li>Modified JS files: <code>/assets/js/apps.js</code> and <code>/assets/css/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap 3.3.3 to Bootstrap 3.3.4
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap/</code></li>
								</ul>
							</li>
							<li>
								FIX: Multi Level Menu Bugfix
								<ul>
									<li>Modified CSS files: <code>/assets/css/style.css</code> and <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Ajax Version Coming Soon Page
								<ul>
									<li>Modified HTML files: <code>/assets/js/coming-soon.demo.js</code> and <code>/assets/js/coming-soon.demo.min.js</code></li>
								</ul>
							</li>
							<li>
								FIX: Ajax Version href="#" rendering error
								<ul>
									<li>Modified HTML files: <code>/assets/js/apps.js</code> and <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 1.6.0 - 23 January 2015</h5>
						<ul>
							<li>
								NEW: Managed Table - Extension Combination
								<ul>
									<li>Added HTML: <code>table_mange_combine.html</code></li>
								</ul>
							</li>
							<li>
								NEW: Chart JS
								<ul>
									<li>Added HTML: <code>chart_js.html</code></li>
									<li>Added PLUGINS: <code>assets/plugins/assets/chart-js/</code></li>
								</ul>
							</li>
							<li>
								NEW: Ajax Documentation
								<ul>
									<li>Updated Directory: <code>documentation/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap 3.3.1 to Bootstrap 3.3.2
								<ul>
									<li>Updated Directory: <code>/assets/plugins/bootstrap/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: FontAwesome 4.2.0 to Bootstrap 4.3.0
								<ul>
									<li>Updated Directory: <code>/assets/plugins/font-awesome/</code></li>
								</ul>
							</li>
							<li>
								FIX: IE 8 - IE 10 Full Height Page
								<ul>
									<li>Modified Directory files: <code>/assets/js/apps.js</code></li>
									<li>Modified Directory files: <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								FIX: Mobile Sidebar Toggle Button
								<ul>
									<li>Modified Directory files: <code>/assets/js/apps.js</code></li>
									<li>Modified Directory files: <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								FIX: Tab Inverse Hover
								<ul>
									<li>Modified Directory files: <code>/assets/css/style.css</code></li>
									<li>Modified Directory files: <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: IE10 Sidebar Menu
								<ul>
									<li>Modified Directory files: <code>/assets/css/style.css</code></li>
									<li>Modified Directory files: <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Minified Sidebar Multiple Level Menu
								<ul>
									<li>Modified Directory files: <code>/assets/css/style.css</code></li>
									<li>Modified Directory files: <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 1.5.0 - 30 November 2014</h5>
						<ul>
							<li>
								NEW: AJAX Version
								<ul>
									<li>New Directory files: <code>/template_content_ajax</code></li>
									<li>New Directory files: <code>/template_content_ajax</code></li>
								</ul>
							</li>
							<li>
								NEW: Color Scheme (Black)
								<ul>
									<li>Modified CSS files: <code>/assets/css/style.css</code></li>
									<li>Modified CSS files: <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Sidebar Color Scheme (Gradient)
								<ul>
									<li>Modified CSS files: <code>/assets/css/theme/default.css</code></li>
									<li>Modified CSS files: <code>/assets/css/theme/blue.css</code></li>
									<li>Modified CSS files: <code>/assets/css/theme/red.css</code></li>
									<li>Modified CSS files: <code>/assets/css/theme/orange.css</code></li>
									<li>Modified CSS files: <code>/assets/css/theme/purple.css</code></li>
									<li>Modified CSS files: <code>/assets/css/theme/black.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Local Storage Panel Position
								<ul>
									<li>Modified Directory files: <code>/assets/js/apps.js</code></li>
									<li>Modified Directory files: <code>/assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								NEW: Ionicons
								<ul>
									<li>Added HTML files: <code>ui_ionicons.html</code></li>
									<li>Added HTML files: <code>page_with_ionicons.html</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/ionicons/</code></li>
								</ul>
							</li>
							<li>
								NEW: DataTables Plugins
								<ul>
									<li>Added HTML files: <code>table_manage_autofill.html</code></li>
									<li>Added HTML files: <code>table_manage_colreorder.html</code></li>
									<li>Added HTML files: <code>table_manage_colvis.html</code></li>
									<li>Added HTML files: <code>table_manage_fixed_columns.html</code></li>
									<li>Added HTML files: <code>table_manage_fixed_header.html</code></li>
									<li>Added HTML files: <code>table_manage_keytable.html</code></li>
									<li>Added HTML files: <code>table_manage_responsive.html</code></li>
									<li>Added HTML files: <code>table_manage_scroller.html</code></li>
									<li>Added HTML files: <code>table_manage_tabletools.html</code></li>
									<li>Updated PLUGINS files: <code>/assets/plugins/DataTables/</code></li>
								</ul>
							</li>
							<li>
								NEW: Pace Loader Plugins
								<ul>
									<li>Modified HTML files: <code>all html files</code></li>
									<li>Added PLUGINS files: <code>/assets/plugins/pace/</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Bootstrap 3.2.0 to Bootstrap 3.3.1
								<ul>
									<li>Modified Directory files: <code>/assets/plugins/bootstrap-3.2.0/</code> to <code>/assets/plugins/bootstrap/</code></li>
									<li>Modified CSS Files: <code>/assets/plugins/bootstrap/css/bootstrap.min.js</code></li>
									<li>Modified JS Files: <code>/assets/plugins/bootstrap/js/bootstrap.min.js</code></li>
								</ul>
							</li>
							<li>
								UPDATE: jQuery 1.8.2 to jQuery 1.9.1
								<ul>
									<li>Modified Directory files: <code>/assets/plugins/jquery-1.8.1/</code> to <code>/assets/plugins/jquery</code></li>
									<li>Added JS Files: <code>/assets/plugins/jquery/jquery-1.9.1.js</code></li>
									<li>Added JS Files: <code>/assets/plugins/jquery/jquery-1.9.1.min.js</code></li>
									<li>Added JS Files: <code>/assets/plugins/jquery/jquery-migrate-1.1.0.js</code></li>
									<li>Added JS Files: <code>/assets/plugins/jquery/jquery-migrate-1.1.0.min.js</code></li>
								</ul>
							</li>
							<li>
								MODIFY: FontAwesome Directory
								<ul>
									<li>Modified Directory files: <code>/assets/plugins/font-awesome-4.2.0/</code> to <code>/assets/plugins/font-awesome/</code></li>
								</ul>
							</li>
							<li>
								FIX: IE8 Font Rendering Issue
								<ul>
									<li>Modified Directory files: <code>/assets/css/style.css</code></li>
									<li>Modified Directory files: <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Google Chrome Fixed Element Rendering Issue
								<ul>
									<li>Modified Directory files: <code>/assets/css/style.css</code></li>
									<li>Modified Directory files: <code>/assets/css/style.min.css</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 1.4.0 - 17 September 2014</h5>
						<ul>
							<li>
								NEW: Front end one page parallax page Added
								<ul>
									<li>Added HTML files: <code>/frontend/one-page-parallax/index.html</code></li>
									<li>Added HTML files: <code>/frontend/one-page-parallax/index_inverse_header.html</code></li>
									<li>Added HTML files: <code>/frontend/one-page-parallax/index_default_header.html</code></li>
									<li>Added HTML files: <code>/frontend/one-page-parallax/extra-element.html</code></li>
								</ul>
							</li>
							<li>
								NEW: Email Inbox v2 page Added
								<ul>
									<li>Added HTML files: <code>email_inbox_v2.html</code></li>
								</ul>
							</li>
							<li>
								NEW: Email Compose v2 page Added
								<ul>
									<li>Added HTML files: <code>email_compose.html</code></li>
								</ul>
							</li>
							<li>
								NEW: Email Detail v2 page Added
								<ul>
									<li>Added HTML files: <code>email_detail.html</code></li>
								</ul>
							</li>
							<li>
								NEW: LESS file Added
								<ul>
									<li>Added LESS files: <code>assets/less</code></li>
								</ul>
							</li>
							<li>
								NEW: Added Meta Setting to Disable Mobile Zooming
								<ul>
									<li>Modified HTML files: <code>ALL</code></li>
								</ul>
							</li>
							<li>
								FIX: Accordion Styling Overflow
								<ul>
									<li>Modified css files: <code>assets/css/style.css</code></li>
									<li>Modified css files: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Mobile Slimscroll Scrolling Speed
								<ul>
									<li>Modified js files: <code>assets/js/apps.js</code></li>
									<li>Modified js files: <code>assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								FIX: Mobile Sidebar Click Event
								<ul>
									<li>Modified js files: <code>assets/js/apps.js</code></li>
									<li>Modified js files: <code>assets/js/apps.min.js</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 1.3.0 - 30 August 2014</h5>
						<ul>
							<li>
								NEW: Dashboard v2 Page
								<ul>
									<li>Added new html files: <code>dashboard_v2.html</code></li>
									<li>Modified css files: <code>assets/css/style.css</code></li>
									<li>Modified css files: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Simple Line Icons Page
								<ul>
									<li>Added new html files: <code>ui_simple_line_icons.html</code></li>
									<li>Added new plugins files: <code>assets/plugins/simple-line-icons</code></li>
								</ul>
							</li>
							<li>
								NEW: Morris Chart Page
								<ul>
									<li>Added new html files: <code>chart_morris.html</code></li>
									<li>Added new plugins files: <code>assets/plugins/morris</code></li>
								</ul>
							</li>
							<li>
								NEW: Page with Line Icons
								<ul>
									<li>Added new html files: <code>page_with_line_icons.html</code></li>
								</ul>
							</li>
							<li>
								NEW: Full Height Content Page
								<ul>
									<li>Added new html files: <code>page_full_height.html</code></li>
									<li>Modified css files: <code>assets/css/style.css</code></li>
									<li>Modified css files: <code>assets/css/style.min.css</code></li>
									<li>Modified css files: <code>assets/css/style-responsive.css</code></li>
									<li>Modified css files: <code>assets/css/style-responsive.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: CSS Helper Page
								<ul>
									<li>Added new html files: <code>helper_css.html</code></li>
								</ul>
							</li>
							<li>
								NEW: Gallery v2 Page
								<ul>
									<li>Added new html files: <code>gallery_v2.html</code></li>
									<li>Modified css files: <code>assets/css/style.css</code></li>
									<li>Modified css files: <code>assets/css/style.min.css</code></li>
									<li>Modified css files: <code>assets/css/style-responsive.css</code></li>
									<li>Modified css files: <code>assets/css/style-responsive.min.css</code></li>
									<li>Added new plugins files: <code>assets/plugins/superbox</code></li>
								</ul>
							</li>
							<li>
								UPDATE: FontAwesome from 4.1.0 to 4.2.0</code>
								<ul>
									<li>Modified css files: <code>assets/css/style.css</code></li>
									<li>Modified css files: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								CHANGE: <code>chart.html</code> to <code>flot-chart.html</code>
							</li>
							<li>
								CHANGE: <code>.widget-states</code> to <code>.widget-stats</code>
								<ul>
									<li>Modified css files: <code>assets/css/style.css</code></li>
									<li>Modified css files: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Typo on Sidebar Link Url
								<ul>
									<li>Modified html files: <code>all html files</code></li>
								</ul>
							</li>
							<li>
								FIX: Minified Sidebar on Tablet
								<ul>
									<li>Modified js file: <code>assets/js/apps.js</code></li>
									<li>Modified js file: <code>assets/js/apps.min.js</code></li>
								</ul>
							</li>
						</ul>
						<h5>Version 1.2.0 - 15 June 2014</h5>
						<ul>
							<li>
								NEW: 5 Color Theme
								<ul>
									<li>Modified html files: <code>all html files</code></li>
									<li>Added new css file: <code>assets/css/theme/default.html</code></li>
									<li>Added new css file: <code>assets/css/theme/blue.html</code></li>
									<li>Added new css file: <code>assets/css/theme/red.html</code></li>
									<li>Added new css file: <code>assets/css/theme/orange.html</code></li>
									<li>Added new css file: <code>assets/css/theme/purple.html</code></li>
									<li>Added new css file: <code>assets/css/theme/black.html</code></li>
									<li>Added new css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Inverse & Default Top Navigation Theme
								<ul>
									<li>Modified html files: <code>all html files</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Sidebar Grid Layout Option
								<ul>
									<li>Modified html files: <code>all html files</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Theme Panel
								<ul>
									<li>Modified html files: <code>all html files</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Sidebar & Header Default & Fixed Option
								<ul>
									<li>Modified html files: <code>all html files</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Page With Two Sidebar
								<ul>
									<li>Added new html files: <code>page_with_two_sidebar.html</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
									<li>Modified js file: <code>assets/js/apps.js</code></li>
									<li>Modified js file: <code>assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								NEW: Login V2
								<ul>
									<li>Added new html files: <code>login_v2.html</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
									<li>Modified js file: <code>assets/js/apps.js</code></li>
									<li>Modified js file: <code>assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								NEW: X-Editable
								<ul>
									<li>Added new html files: <code>form_editable.html</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Multiple File Upload
								<ul>
									<li>Added new html files: <code>form_multiple_upload.html</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Form Slider + Switcher Page
								<ul>
									<li>Added new html files: <code>form_slider_switcher.html</code></li>
									<li>Added new plugins file: <code>assets/plugins/switchery</code></li>
								</ul>
							</li>
							<li>
								NEW: Wizards + Validation Page
								<ul>
									<li>Added new html files: <code>form_wizards_validation.html</code></li>
									<li>Updated plugins file: <code>assets/plugins/parsley</code></li>
								</ul>
							</li>
							<li>
								UPDATE: Plugins Parsley 2.0 
								<ul>
									<li>Updated html files: <code>form_wizards.html</code></li>
									<li>Updated plugins file: <code>assets/plugins/parsley</code></li>
								</ul>
							</li>
							<li>
								UI: Dashboard widget state & line chart improvement
								<ul>
									<li>Modified html files: <code>index.html</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Mobile Top Navigation Fixed Top
								<ul>
									<li>Modified html files: <code>all html files</code></li>
									<li>Modified css file: <code>assets/css/style-responsive.css</code></li>
									<li>Modified css file: <code>assets/css/style-responsive.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Mobile Sidebar Fixed Right Position
								<ul>
									<li>Modified html files: <code>all html files</code></li>
									<li>Modified css file: <code>assets/css/style-responsive.css</code></li>
									<li>Modified css file: <code>assets/css/style-responsive.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Panel Remove Action Tooltip
								<ul>
									<li>Modified js file: <code>assets/js/apps.js</code></li>
									<li>Modified js file: <code>assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								FIX: Mozilla Firefox Sidebar Minimized
								<ul>
									<li>Modified js file: <code>assets/js/apps.js</code></li>
									<li>Modified js file: <code>assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								FIX: Sidebar DOM Element Didn't Functioning 
								<ul>
									<li>Modified js file: <code>assets/js/apps.js</code></li>
									<li>Modified js file: <code>assets/js/apps.min.js</code></li>
								</ul>
							</li>
							<li>
								FIX: Sidebar Uncollapsed Sub Menu After Minify
								<ul>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Old Browser Transparent Background Styling Support 
								<ul>
									<li>Added new folder: <code>assets/img/transparent/</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
						</ul>
						<p>&nbsp;</p>
						<h5>Version 1.1.0 – 30 May 2014</h5>
						<ul>
							<li>
								NEW: Email Template
								<ul>
									<li>Added new html file: <code>email_system.html</code></li>
									<li>Added new html file: <code>email_newsletter.html</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Timeline Page
								<ul>
									<li>Added new html file: <code>extra_timeline.html</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
									<li>Modified css file: <code>assets/css/style-responsive.css</code></li>
									<li>Modified css file: <code>assets/css/style-responsive.min.css</code></li>
								</ul>
							</li>
							<li>
								NEW: Coming Soon Page
								<ul>
									<li>Added new html file: <code>extra_coming_soon.html</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
									<li>Modified css file: <code>assets/css/style-responsive.css</code></li>
									<li>Modified css file: <code>assets/css/style-responsive.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Sidebar Sub Menu Caret Styling
								<ul>
									<li>Modified html files: <code>all html files</code></li>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Modal Backdrop Opacity
								<ul>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Login form in mobile landscape view 
								<ul>
									<li>Modified css file: <code>assets/css/style.css</code></li>
									<li>Modified css file: <code>assets/css/style.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Mobile View Search Box in header
								<ul>
									<li>Modified html files: <code>all html files</code></li>
									<li>Modified css file: <code>assets/css/style-responsive.css</code></li>
									<li>Modified css file: <code>assets/css/style-responsive.min.css</code></li>
								</ul>
							</li>
							<li>
								FIX: Icon page broken on mobile
								<ul>
									<li>Modified file: <code>ui_icons.html</code></li>
								</ul>
							</li>
							<li>
								FIX: Dashboard Tab Panel on mobile
								<ul>
									<li>Modified html files: <code>index.html</code></li>
									<li>Modified css file: <code>assets/css/style-responsive.css</code></li>
									<li>Modified css file: <code>assets/css/style-responsive.min.css</code></li>
								</ul>
							</li>
						</ul>
					</div>
				</div><!-- end row-fluid -->
			</div><!-- end span12 -->
		</div><!-- end row-fluid -->
	</div><!-- end container -->
	
	<footer class="footer">
		<div class="container text-left">
			<p>Once again, thank you so much for purchasing this theme. As I said at the beginning, I'd be glad to help you if you have any questions relating to this theme. No guarantees, but I'll do my best to assist. If you have a more general question relating to the themes, you might consider visiting the forums and asking your question via <a href="mailTo:<EMAIL>">email</a>.</p> 
			<br />
			<p class="append-bottom alt large"><strong>Sean Ngu</strong></p>
			<p><a href="#top">Go To Table of Contents</a></p>
		</div>
	</footer><!-- end footer -->
	
	<script src="assets/bootstrap/js/jquery.js"></script>
	<script src="assets/bootstrap/js/bootstrap-transition.js"></script>
	<script src="assets/bootstrap/js/bootstrap-alert.js"></script>
	<script src="assets/bootstrap/js/bootstrap-modal.js"></script>
	<script src="assets/bootstrap/js/bootstrap-dropdown.js"></script>
	<script src="assets/bootstrap/js/bootstrap-scrollspy.js"></script>
	<script src="assets/bootstrap/js/bootstrap-tab.js"></script>
	<script src="assets/bootstrap/js/bootstrap-tooltip.js"></script>
	<script src="assets/bootstrap/js/bootstrap-popover.js"></script>
	<script src="assets/bootstrap/js/bootstrap-button.js"></script>
	<script src="assets/bootstrap/js/bootstrap-collapse.js"></script>
	<script src="assets/bootstrap/js/bootstrap-carousel.js"></script>
	<script src="assets/bootstrap/js/bootstrap-typeahead.js"></script>
	<script src="assets/bootstrap/js/bootstrap-affix.js"></script>

	<script src="assets/bootstrap/js/holder/holder.js"></script>
	<script src="assets/bootstrap/js/google-code-prettify/prettify.js"></script>
	<script src="assets/bootstrap/js/application.js"></script>
</body>
</html>